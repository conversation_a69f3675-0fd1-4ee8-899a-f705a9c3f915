#!/bin/bash

# Legal AI Platform Deployment Script

set -e

echo "🚀 Starting Legal AI Platform deployment..."

# Check if Dock<PERSON> is running
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker is not running. Please start Docker and try again."
    exit 1
fi

# Check if docker-compose is available
if ! command -v docker-compose &> /dev/null; then
    echo "❌ docker-compose is not installed. Please install it and try again."
    exit 1
fi

# Create .env file if it doesn't exist
if [ ! -f .env ]; then
    echo "📝 Creating .env file from template..."
    cp .env.example .env
    echo "⚠️  Please edit .env file with your actual configuration values"
fi

# Build and start services
echo "🔨 Building and starting services..."
docker-compose down --remove-orphans
docker-compose build --no-cache
docker-compose up -d

# Wait for services to be ready
echo "⏳ Waiting for services to start..."
sleep 30

# Check service health
echo "🏥 Checking service health..."
services=(
    "api_gateway:8000"
    "session_hub:8001"
    "case_router:8002"
    "intake_engine:8003"
    "voice_adapter:8004"
    "chat_adapter:8005"
    "email_adapter:8006"
    "file_ingestion:8007"
    "reasoning_agent:8008"
    "demand_generator:8009"
    "complaint_generator:8010"
    "approval_service:8011"
    "notification_service:8012"
)

healthy_services=0
total_services=${#services[@]}

for service in "${services[@]}"; do
    service_name=$(echo $service | cut -d: -f1)
    port=$(echo $service | cut -d: -f2)
    
    if curl -s -f "http://localhost:$port/health" > /dev/null; then
        echo "✅ $service_name is healthy"
        ((healthy_services++))
    else
        echo "❌ $service_name is not responding"
    fi
done

# Initialize database
echo "🗄️  Initializing database..."
if docker-compose exec -T postgres psql -U postgres -d legal_ai -c "SELECT 1;" > /dev/null 2>&1; then
    echo "✅ Database is accessible"
    
    # Run database initialization
    if python3 database/init.py; then
        echo "✅ Database initialized successfully"
    else
        echo "⚠️  Database initialization failed, but continuing..."
    fi
else
    echo "❌ Database is not accessible"
fi

# Display deployment summary
echo ""
echo "📊 Deployment Summary:"
echo "====================="
echo "Healthy services: $healthy_services/$total_services"
echo ""

if [ $healthy_services -eq $total_services ]; then
    echo "🎉 All services are running successfully!"
    echo ""
    echo "🌐 Access points:"
    echo "  - API Gateway: http://localhost:8000"
    echo "  - API Documentation: http://localhost:8000/docs"
    echo "  - Service Status: http://localhost:8000/services/status"
    echo ""
    echo "🔑 Default login credentials:"
    echo "  - Username: admin"
    echo "  - Password: password"
    echo ""
    echo "📚 Next steps:"
    echo "  1. Update .env file with your actual configuration"
    echo "  2. Configure Twilio webhooks to point to your endpoints"
    echo "  3. Set up email service credentials"
    echo "  4. Configure Slack webhook URL"
    echo "  5. Test the system with sample data"
else
    echo "⚠️  Some services are not healthy. Check the logs:"
    echo "   docker-compose logs [service_name]"
fi

echo ""
echo "📋 Useful commands:"
echo "  - View logs: docker-compose logs -f [service_name]"
echo "  - Stop services: docker-compose down"
echo "  - Restart services: docker-compose restart"
echo "  - View service status: curl http://localhost:8000/services/status"
