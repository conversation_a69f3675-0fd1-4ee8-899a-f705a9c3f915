#!/bin/bash

# Legal AI Platform API Testing Script

set -e

API_BASE="http://localhost:8000"
TOKEN=""

echo "🧪 Testing Legal AI Platform API..."

# Function to make authenticated requests
auth_request() {
    local method=$1
    local endpoint=$2
    local data=$3
    
    if [ -n "$data" ]; then
        curl -s -X $method \
            -H "Authorization: Bearer $TOKEN" \
            -H "Content-Type: application/json" \
            -d "$data" \
            "$API_BASE$endpoint"
    else
        curl -s -X $method \
            -H "Authorization: Bearer $TOKEN" \
            "$API_BASE$endpoint"
    fi
}

# Test 1: Health check
echo "1️⃣  Testing health check..."
health_response=$(curl -s "$API_BASE/health")
if echo "$health_response" | grep -q "healthy"; then
    echo "✅ Health check passed"
else
    echo "❌ Health check failed: $health_response"
    exit 1
fi

# Test 2: Authentication
echo "2️⃣  Testing authentication..."
auth_response=$(curl -s -X POST \
    -H "Content-Type: application/json" \
    -d '{"username": "admin", "password": "password"}' \
    "$API_BASE/auth/login")

if echo "$auth_response" | grep -q "access_token"; then
    TOKEN=$(echo "$auth_response" | grep -o '"access_token":"[^"]*"' | cut -d'"' -f4)
    echo "✅ Authentication successful"
else
    echo "❌ Authentication failed: $auth_response"
    exit 1
fi

# Test 3: Service status
echo "3️⃣  Testing service status..."
status_response=$(auth_request "GET" "/services/status")
if echo "$status_response" | grep -q "session_hub"; then
    echo "✅ Service status check passed"
else
    echo "❌ Service status check failed: $status_response"
fi

# Test 4: Test webhook endpoints (no auth required)
echo "4️⃣  Testing webhook endpoints..."

# Test SMS webhook
sms_response=$(curl -s -X POST \
    -H "Content-Type: application/x-www-form-urlencoded" \
    -d "MessageSid=test123&From=%2B**********&Body=I%20was%20in%20a%20car%20accident" \
    "$API_BASE/webhooks/sms")

if [ $? -eq 0 ]; then
    echo "✅ SMS webhook test passed"
else
    echo "❌ SMS webhook test failed"
fi

# Test voice webhook
voice_response=$(curl -s -X POST \
    -H "Content-Type: application/x-www-form-urlencoded" \
    -d "CallSid=test123&From=%2B**********" \
    "$API_BASE/webhooks/voice")

if [ $? -eq 0 ]; then
    echo "✅ Voice webhook test passed"
else
    echo "❌ Voice webhook test failed"
fi

# Test 5: Create a test case (if session hub is working)
echo "5️⃣  Testing case creation..."
case_data='{
    "client_name": "Test Client",
    "client_phone": "+**********",
    "client_email": "<EMAIL>"
}'

# This would test actual case creation if the endpoint exists
echo "ℹ️  Case creation test skipped (endpoint may not be implemented)"

# Test 6: Test individual service health
echo "6️⃣  Testing individual service health..."
services=(
    "session_hub:8001"
    "case_router:8002"
    "intake_engine:8003"
    "voice_adapter:8004"
    "chat_adapter:8005"
    "email_adapter:8006"
)

healthy_count=0
total_count=${#services[@]}

for service in "${services[@]}"; do
    service_name=$(echo $service | cut -d: -f1)
    port=$(echo $service | cut -d: -f2)
    
    if curl -s -f "http://localhost:$port/health" > /dev/null; then
        echo "✅ $service_name health check passed"
        ((healthy_count++))
    else
        echo "❌ $service_name health check failed"
    fi
done

# Summary
echo ""
echo "📊 Test Summary:"
echo "==============="
echo "Individual service health: $healthy_count/$total_count"

if [ $healthy_count -eq $total_count ]; then
    echo "🎉 All tests passed! The system is working correctly."
else
    echo "⚠️  Some services are not responding. Check the logs for details."
fi

echo ""
echo "🔍 Additional testing suggestions:"
echo "  - Test with real Twilio webhooks"
echo "  - Test file upload functionality"
echo "  - Test email ingestion"
echo "  - Test case workflow end-to-end"
echo "  - Load test with multiple concurrent requests"
