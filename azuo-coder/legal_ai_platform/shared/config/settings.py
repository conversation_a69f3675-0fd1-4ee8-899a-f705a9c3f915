import os
from typing import Optional

class Settings:
    """Application settings loaded from environment variables"""
    
    # Database
    DATABASE_URL: str = os.getenv("DATABASE_URL", "postgresql://postgres:password@localhost:5432/legal_ai")
    REDIS_URL: str = os.getenv("REDIS_URL", "redis://localhost:6379")
    
    # JWT
    JWT_SECRET: str = os.getenv("JWT_SECRET", "your-secret-key")
    JWT_ALGORITHM: str = "HS256"
    JWT_EXPIRATION_HOURS: int = int(os.getenv("JWT_EXPIRATION_HOURS", "24"))
    
    # Twilio
    TWILIO_ACCOUNT_SID: Optional[str] = os.getenv("TWILIO_ACCOUNT_SID")
    TWILIO_AUTH_TOKEN: Optional[str] = os.getenv("TWILIO_AUTH_TOKEN")
    TWILIO_PHONE_NUMBER: Optional[str] = os.getenv("TWILIO_PHONE_NUMBER")
    
    # Email
    SMTP_SERVER: str = os.getenv("SMTP_SERVER", "smtp.gmail.com")
    SMTP_PORT: int = int(os.getenv("SMTP_PORT", "587"))
    SMTP_USERNAME: Optional[str] = os.getenv("SMTP_USERNAME")
    SMTP_PASSWORD: Optional[str] = os.getenv("SMTP_PASSWORD")
    
    # Slack
    SLACK_WEBHOOK_URL: Optional[str] = os.getenv("SLACK_WEBHOOK_URL")
    
    # Service URLs (for inter-service communication)
    SESSION_HUB_URL: str = os.getenv("SESSION_HUB_URL", "http://session_hub:8000")
    CASE_ROUTER_URL: str = os.getenv("CASE_ROUTER_URL", "http://case_router:8000")
    INTAKE_ENGINE_URL: str = os.getenv("INTAKE_ENGINE_URL", "http://intake_engine:8000")
    VOICE_ADAPTER_URL: str = os.getenv("VOICE_ADAPTER_URL", "http://voice_adapter:8000")
    CHAT_ADAPTER_URL: str = os.getenv("CHAT_ADAPTER_URL", "http://chat_adapter:8000")
    EMAIL_ADAPTER_URL: str = os.getenv("EMAIL_ADAPTER_URL", "http://email_adapter:8000")
    FILE_INGESTION_URL: str = os.getenv("FILE_INGESTION_URL", "http://file_ingestion:8000")
    REASONING_AGENT_URL: str = os.getenv("REASONING_AGENT_URL", "http://reasoning_agent:8000")
    DEMAND_GENERATOR_URL: str = os.getenv("DEMAND_GENERATOR_URL", "http://demand_generator:8000")
    COMPLAINT_GENERATOR_URL: str = os.getenv("COMPLAINT_GENERATOR_URL", "http://complaint_generator:8000")
    APPROVAL_SERVICE_URL: str = os.getenv("APPROVAL_SERVICE_URL", "http://approval_service:8000")
    NOTIFICATION_SERVICE_URL: str = os.getenv("NOTIFICATION_SERVICE_URL", "http://notification_service:8000")
    
    # File Storage
    S3_BUCKET: Optional[str] = os.getenv("S3_BUCKET")
    S3_REGION: str = os.getenv("S3_REGION", "us-east-1")
    AWS_ACCESS_KEY_ID: Optional[str] = os.getenv("AWS_ACCESS_KEY_ID")
    AWS_SECRET_ACCESS_KEY: Optional[str] = os.getenv("AWS_SECRET_ACCESS_KEY")
    
    # Application
    LOG_LEVEL: str = os.getenv("LOG_LEVEL", "INFO")
    DEBUG: bool = os.getenv("DEBUG", "false").lower() == "true"
    
    # Rate limiting
    RATE_LIMIT_REQUESTS: int = int(os.getenv("RATE_LIMIT_REQUESTS", "100"))
    RATE_LIMIT_WINDOW: int = int(os.getenv("RATE_LIMIT_WINDOW", "60"))  # seconds

# Global settings instance
settings = Settings()
