from abc import ABC, abstractmethod
from typing import List, Optional, Dict, Any
from datetime import datetime
from shared.models.case import Case, CaseFact, TimelineEvent, Document, NormalizedMessage

class CaseRepository(ABC):
    """Interface for case data persistence in Postgres"""
    
    @abstractmethod
    async def create_case(self, case: Case) -> str:
        """Create a new case and return its ID"""
        pass
    
    @abstractmethod
    async def get_case(self, case_id: str) -> Optional[Case]:
        """Retrieve a case by ID"""
        pass
    
    @abstractmethod
    async def update_case(self, case: Case) -> bool:
        """Update an existing case"""
        pass
    
    @abstractmethod
    async def find_case_by_client_info(self, phone: str = None, email: str = None) -> Optional[Case]:
        """Find case by client contact information"""
        pass
    
    @abstractmethod
    async def add_fact(self, fact: CaseFact) -> str:
        """Add a fact to a case"""
        pass
    
    @abstractmethod
    async def get_facts(self, case_id: str) -> List[CaseFact]:
        """Get all facts for a case"""
        pass
    
    @abstractmethod
    async def add_timeline_event(self, event: TimelineEvent) -> str:
        """Add a timeline event to a case"""
        pass
    
    @abstractmethod
    async def get_timeline(self, case_id: str) -> List[TimelineEvent]:
        """Get timeline events for a case"""
        pass
    
    @abstractmethod
    async def add_document(self, document: Document) -> str:
        """Add a document record to a case"""
        pass
    
    @abstractmethod
    async def get_documents(self, case_id: str) -> List[Document]:
        """Get all documents for a case"""
        pass
    
    @abstractmethod
    async def store_message(self, message: NormalizedMessage) -> str:
        """Store a normalized message"""
        pass
    
    @abstractmethod
    async def get_case_messages(self, case_id: str) -> List[NormalizedMessage]:
        """Get all messages for a case"""
        pass

class DocumentStorage(ABC):
    """Interface for document storage in S3"""
    
    @abstractmethod
    async def upload_file(self, file_content: bytes, filename: str, case_id: str) -> str:
        """Upload a file and return its storage path"""
        pass
    
    @abstractmethod
    async def download_file(self, file_path: str) -> bytes:
        """Download a file by its storage path"""
        pass
    
    @abstractmethod
    async def delete_file(self, file_path: str) -> bool:
        """Delete a file from storage"""
        pass
    
    @abstractmethod
    async def get_file_url(self, file_path: str, expires_in: int = 3600) -> str:
        """Get a presigned URL for file access"""
        pass

class SessionCache(ABC):
    """Interface for session caching in Redis"""
    
    @abstractmethod
    async def set_session(self, case_id: str, session_data: Dict[str, Any], ttl: int = 3600) -> bool:
        """Store session data with TTL"""
        pass
    
    @abstractmethod
    async def get_session(self, case_id: str) -> Optional[Dict[str, Any]]:
        """Retrieve session data"""
        pass
    
    @abstractmethod
    async def update_session(self, case_id: str, updates: Dict[str, Any]) -> bool:
        """Update specific fields in session data"""
        pass
    
    @abstractmethod
    async def delete_session(self, case_id: str) -> bool:
        """Delete session data"""
        pass
    
    @abstractmethod
    async def set_cache(self, key: str, value: Any, ttl: int = 300) -> bool:
        """Set a cache value with TTL"""
        pass
    
    @abstractmethod
    async def get_cache(self, key: str) -> Optional[Any]:
        """Get a cache value"""
        pass
    
    @abstractmethod
    async def delete_cache(self, key: str) -> bool:
        """Delete a cache value"""
        pass
