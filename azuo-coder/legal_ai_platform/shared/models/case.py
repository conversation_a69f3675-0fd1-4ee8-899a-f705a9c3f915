from pydantic import BaseModel, Field
from typing import Optional, List, Dict, Any
from datetime import datetime
from enum import Enum

class CaseStatus(str, Enum):
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    INTAKE_COMPLETE = "intake_complete"
    DRAFT_READY = "draft_ready"
    UNDER_REVIEW = "under_review"
    APPROVED = "approved"
    FILED = "filed"
    CLOSED = "closed"

class ChannelType(str, Enum):
    VOICE = "voice"
    SMS = "sms"
    EMAIL = "email"
    CHAT = "chat"
    WHATSAPP = "whatsapp"

class DocumentType(str, Enum):
    EVIDENCE = "evidence"
    MEDICAL_RECORD = "medical_record"
    POLICE_REPORT = "police_report"
    INSURANCE_DOCUMENT = "insurance_document"
    CORRESPONDENCE = "correspondence"
    OTHER = "other"

class NormalizedMessage(BaseModel):
    case_id: Optional[str] = None
    channel: ChannelType
    text: str
    attachments: List[str] = Field(default_factory=list)
    sender: Optional[str] = None
    timestamp: datetime = Field(default_factory=datetime.utcnow)
    metadata: Dict[str, Any] = Field(default_factory=dict)

class CaseFact(BaseModel):
    id: Optional[str] = None
    case_id: str
    fact_type: str  # e.g., "incident_date", "injury_type", "location"
    value: str
    confidence: float = Field(ge=0.0, le=1.0)
    source: str  # message_id or document_id
    extracted_at: datetime = Field(default_factory=datetime.utcnow)
    verified: bool = False

class TimelineEvent(BaseModel):
    id: Optional[str] = None
    case_id: str
    event_date: datetime
    event_type: str
    description: str
    source: str
    confidence: float = Field(ge=0.0, le=1.0)
    created_at: datetime = Field(default_factory=datetime.utcnow)

class Document(BaseModel):
    id: Optional[str] = None
    case_id: str
    filename: str
    document_type: DocumentType
    file_path: str  # S3 path
    file_size: int
    mime_type: str
    uploaded_at: datetime = Field(default_factory=datetime.utcnow)
    processed: bool = False
    ocr_text: Optional[str] = None
    metadata: Dict[str, Any] = Field(default_factory=dict)

class Case(BaseModel):
    id: Optional[str] = None
    client_name: Optional[str] = None
    client_phone: Optional[str] = None
    client_email: Optional[str] = None
    status: CaseStatus = CaseStatus.PENDING
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)

    # Case details
    incident_date: Optional[datetime] = None
    incident_location: Optional[str] = None
    incident_description: Optional[str] = None
    injury_description: Optional[str] = None

    # Extracted facts and timeline
    facts: List[CaseFact] = Field(default_factory=list)
    timeline: List[TimelineEvent] = Field(default_factory=list)
    documents: List[Document] = Field(default_factory=list)

    # AI-generated content
    damage_estimate: Optional[float] = None
    demand_letter: Optional[str] = None
    complaint_draft: Optional[str] = None

    # Workflow
    assigned_attorney: Optional[str] = None
    approval_status: Optional[str] = None
    flags: List[str] = Field(default_factory=list)  # inconsistencies, missing info

    # Metadata
    metadata: Dict[str, Any] = Field(default_factory=dict)