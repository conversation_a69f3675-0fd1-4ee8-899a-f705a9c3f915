import httpx
from typing import Dict, Any, Optional
import logging

logger = logging.getLogger(__name__)

class HTTPClient:
    """Shared HTTP client with retry logic and error handling"""
    
    def __init__(self, timeout: float = 30.0, retries: int = 3):
        self.timeout = timeout
        self.retries = retries
    
    async def request(self, method: str, url: str, **kwargs) -> Dict[str, Any]:
        """Make HTTP request with retry logic"""
        
        for attempt in range(self.retries):
            try:
                async with httpx.AsyncClient(timeout=self.timeout) as client:
                    response = await client.request(method, url, **kwargs)
                    
                    if response.status_code < 400:
                        try:
                            return {
                                "status_code": response.status_code,
                                "data": response.json(),
                                "success": True
                            }
                        except:
                            return {
                                "status_code": response.status_code,
                                "data": response.text,
                                "success": True
                            }
                    else:
                        logger.warning(f"HTTP {response.status_code} from {url}: {response.text}")
                        if attempt == self.retries - 1:  # Last attempt
                            return {
                                "status_code": response.status_code,
                                "error": response.text,
                                "success": False
                            }
                        
            except httpx.RequestError as e:
                logger.warning(f"Request error to {url} (attempt {attempt + 1}): {e}")
                if attempt == self.retries - 1:  # Last attempt
                    return {
                        "status_code": 0,
                        "error": str(e),
                        "success": False
                    }
        
        return {
            "status_code": 0,
            "error": "Max retries exceeded",
            "success": False
        }
    
    async def get(self, url: str, params: Optional[Dict] = None, headers: Optional[Dict] = None) -> Dict[str, Any]:
        """GET request"""
        return await self.request("GET", url, params=params, headers=headers)
    
    async def post(self, url: str, json: Optional[Dict] = None, data: Optional[Dict] = None, 
                  headers: Optional[Dict] = None) -> Dict[str, Any]:
        """POST request"""
        return await self.request("POST", url, json=json, data=data, headers=headers)
    
    async def put(self, url: str, json: Optional[Dict] = None, data: Optional[Dict] = None,
                 headers: Optional[Dict] = None) -> Dict[str, Any]:
        """PUT request"""
        return await self.request("PUT", url, json=json, data=data, headers=headers)
    
    async def delete(self, url: str, headers: Optional[Dict] = None) -> Dict[str, Any]:
        """DELETE request"""
        return await self.request("DELETE", url, headers=headers)

# Global HTTP client instance
http_client = HTTPClient()
