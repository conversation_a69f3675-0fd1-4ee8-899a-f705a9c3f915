# Legal AI Platform Environment Configuration

# Database
DATABASE_URL=postgresql://postgres:password@localhost:5432/legal_ai
REDIS_URL=redis://localhost:6379

# JWT Authentication
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRATION_HOURS=24

# Twilio Configuration
TWILIO_ACCOUNT_SID=your-twilio-account-sid
TWILIO_AUTH_TOKEN=your-twilio-auth-token
TWILIO_PHONE_NUMBER=+**********

# Email Configuration
SMTP_SERVER=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your-app-password

# Slack Configuration
SLACK_WEBHOOK_URL=https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK

# AWS S3 Configuration (for file storage)
S3_BUCKET=legal-ai-documents
S3_REGION=us-east-1
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key

# Application Configuration
LOG_LEVEL=INFO
DEBUG=false

# Rate Limiting
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_WINDOW=60

# Service URLs (for development/testing)
SESSION_HUB_URL=http://localhost:8001
CASE_ROUTER_URL=http://localhost:8002
INTAKE_ENGINE_URL=http://localhost:8003
VOICE_ADAPTER_URL=http://localhost:8004
CHAT_ADAPTER_URL=http://localhost:8005
EMAIL_ADAPTER_URL=http://localhost:8006
FILE_INGESTION_URL=http://localhost:8007
REASONING_AGENT_URL=http://localhost:8008
DEMAND_GENERATOR_URL=http://localhost:8009
COMPLAINT_GENERATOR_URL=http://localhost:8010
APPROVAL_SERVICE_URL=http://localhost:8011
NOTIFICATION_SERVICE_URL=http://localhost:8012
