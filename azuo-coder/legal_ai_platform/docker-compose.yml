version: "3.8"
services:
  # API Gateway
  api_gateway:
    build: ./services/api_gateway
    ports:
      - "8000:8000"
    depends_on:
      - session_hub
      - case_router
    environment:
      - JWT_SECRET=your-secret-key

  # Core Services
  session_hub:
    build: ./services/session_hub
    ports:
      - "8001:8000"
    depends_on:
      - postgres
      - redis
    environment:
      - DATABASE_URL=********************************************/legal_ai
      - REDIS_URL=redis://redis:6379

  case_router:
    build: ./services/case_router
    ports:
      - "8002:8000"
    depends_on:
      - session_hub

  intake_engine:
    build: ./services/intake_engine
    ports:
      - "8003:8000"
    depends_on:
      - session_hub

  # Channel Adapters
  voice_adapter:
    build: ./services/voice_adapter
    ports:
      - "8004:8000"
    depends_on:
      - case_router

  chat_adapter:
    build: ./services/chat_adapter
    ports:
      - "8005:8000"
    depends_on:
      - case_router

  email_adapter:
    build: ./services/email_adapter
    ports:
      - "8006:8000"
    depends_on:
      - case_router

  # Processing Services
  file_ingestion:
    build: ./services/file_ingestion
    ports:
      - "8007:8000"
    depends_on:
      - session_hub

  reasoning_agent:
    build: ./services/reasoning_agent
    ports:
      - "8008:8000"
    depends_on:
      - session_hub

  demand_generator:
    build: ./services/demand_generator
    ports:
      - "8009:8000"
    depends_on:
      - session_hub

  complaint_generator:
    build: ./services/complaint_generator
    ports:
      - "8010:8000"
    depends_on:
      - session_hub

  approval_service:
    build: ./services/approval_service
    ports:
      - "8011:8000"
    depends_on:
      - session_hub
      - notification_service

  notification_service:
    build: ./services/notification_service
    ports:
      - "8012:8000"
    environment:
      - SMTP_SERVER=smtp.gmail.com
      - SMTP_PORT=587
      - TWILIO_ACCOUNT_SID=your-twilio-sid
      - TWILIO_AUTH_TOKEN=your-twilio-token

  # Storage Layer
  postgres:
    image: postgres:15
    ports:
      - "5432:5432"
    environment:
      POSTGRES_DB: legal_ai
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
    volumes:
      - postgres_data:/var/lib/postgresql/data

  redis:
    image: redis:6
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data

volumes:
  postgres_data:
  redis_data: