from fastapi import FastAPI, HTTPException
from typing import Dict, Any, List, Optional
import httpx
import json
import re
from datetime import datetime

from shared.models.case import NormalizedMessage, CaseStatus

app = FastAPI(title="Intake Engine Service")

class IntakeEngine:
    def __init__(self):
        self.session_hub_url = "http://session_hub:8000"
        self.llm_api_url = "http://llm_service:8000"  # Placeholder for LLM service
        
        # Fact extraction patterns
        self.fact_patterns = {
            "incident_date": [
                r"(?:on|happened on|occurred on|was on)\s+(\d{1,2}[/-]\d{1,2}[/-]\d{2,4})",
                r"(\d{1,2}[/-]\d{1,2}[/-]\d{2,4})",
                r"(yesterday|today|last week|last month)"
            ],
            "injury_type": [
                r"(broke|fractured|injured|hurt|damaged|sprained|torn)\s+(?:my\s+)?(\w+)",
                r"(?:have|got)\s+(?:a\s+)?(broken|fractured|sprained|torn)\s+(\w+)",
                r"(whiplash|concussion|bruises|cuts|burns)"
            ],
            "location": [
                r"(?:at|in|on|near)\s+([^.!?]+?)(?:\s+(?:when|where|and|but)|\.|$)",
                r"(?:happened|occurred|was)\s+(?:at|in|on|near)\s+([^.!?]+?)(?:\s+(?:when|where|and|but)|\.|$)"
            ],
            "vehicle_type": [
                r"(car|truck|motorcycle|bike|bicycle|bus|van|suv)",
                r"driving\s+(?:a|my)\s+(\w+)",
                r"in\s+(?:a|my|the)\s+(\w+)"
            ],
            "other_party": [
                r"(?:hit by|struck by|other driver|they|their)\s+([^.!?]+?)(?:\s+(?:was|had|and|but)|\.|$)"
            ]
        }
        
        # Follow-up questions based on missing facts
        self.follow_up_questions = {
            "incident_date": [
                "When exactly did this incident happen? Please provide the date.",
                "Can you tell me the specific date of the accident?"
            ],
            "injury_type": [
                "What specific injuries did you sustain?",
                "Can you describe what parts of your body were injured?"
            ],
            "location": [
                "Where exactly did this incident occur? Please provide the address or location.",
                "Can you tell me the specific location where this happened?"
            ],
            "incident_description": [
                "Can you provide more details about how the incident happened?",
                "Please describe the sequence of events leading to the incident."
            ],
            "medical_treatment": [
                "Did you receive medical treatment? If so, where and when?",
                "Have you seen a doctor about your injuries?"
            ],
            "police_report": [
                "Was a police report filed? Do you have the report number?",
                "Did the police come to the scene?"
            ],
            "insurance": [
                "Do you have insurance? Have you contacted your insurance company?",
                "What insurance information do you have for the other party?"
            ]
        }
    
    async def extract_facts(self, text: str, case_id: str) -> List[Dict[str, Any]]:
        """Extract facts from text using pattern matching and LLM"""
        extracted_facts = []
        
        # Pattern-based extraction
        for fact_type, patterns in self.fact_patterns.items():
            for pattern in patterns:
                matches = re.findall(pattern, text, re.IGNORECASE)
                if matches:
                    for match in matches:
                        if isinstance(match, tuple):
                            value = " ".join(match).strip()
                        else:
                            value = match.strip()
                        
                        if value:
                            extracted_facts.append({
                                "fact_type": fact_type,
                                "value": value,
                                "confidence": 0.8,  # Pattern-based confidence
                                "source": "pattern_extraction"
                            })
        
        # LLM-based extraction (placeholder)
        llm_facts = await self._llm_extract_facts(text)
        extracted_facts.extend(llm_facts)
        
        return extracted_facts
    
    async def _llm_extract_facts(self, text: str) -> List[Dict[str, Any]]:
        """Use LLM to extract facts (placeholder implementation)"""
        # This would call an actual LLM service
        # For now, return empty list
        return []
    
    async def generate_follow_up_questions(self, case_id: str, session_data: Dict[str, Any]) -> List[str]:
        """Generate follow-up questions based on missing information"""
        
        # Get current facts for the case
        async with httpx.AsyncClient() as client:
            try:
                response = await client.get(f"{self.session_hub_url}/cases/{case_id}/facts")
                facts = response.json() if response.status_code == 200 else []
            except:
                facts = []
        
        # Determine what facts are missing
        existing_fact_types = {fact.get("fact_type") for fact in facts}
        required_facts = {
            "incident_date", "injury_type", "location", "incident_description",
            "medical_treatment", "police_report", "insurance"
        }
        
        missing_facts = required_facts - existing_fact_types
        
        # Generate questions for missing facts
        questions = []
        for fact_type in missing_facts:
            if fact_type in self.follow_up_questions:
                questions.extend(self.follow_up_questions[fact_type][:1])  # Take first question
        
        return questions[:3]  # Limit to 3 questions at a time
    
    async def assess_intake_completeness(self, case_id: str) -> Dict[str, Any]:
        """Assess if intake is complete enough to proceed"""
        
        # Get current facts
        async with httpx.AsyncClient() as client:
            try:
                response = await client.get(f"{self.session_hub_url}/cases/{case_id}/facts")
                facts = response.json() if response.status_code == 200 else []
            except:
                facts = []
        
        fact_types = {fact.get("fact_type") for fact in facts}
        
        # Minimum required facts for completion
        minimum_required = {"incident_date", "injury_type", "location"}
        recommended_facts = {"incident_description", "medical_treatment"}
        
        has_minimum = minimum_required.issubset(fact_types)
        has_recommended = recommended_facts.issubset(fact_types)
        
        completeness_score = len(fact_types & (minimum_required | recommended_facts)) / len(minimum_required | recommended_facts)
        
        return {
            "is_complete": has_minimum,
            "is_comprehensive": has_minimum and has_recommended,
            "completeness_score": completeness_score,
            "missing_minimum": list(minimum_required - fact_types),
            "missing_recommended": list(recommended_facts - fact_types)
        }
    
    async def process_message(self, routing_data: Dict[str, Any]) -> Dict[str, Any]:
        """Process an intake message"""
        message = NormalizedMessage(**routing_data["message"])
        case_id = routing_data["case_id"]
        session_data = routing_data.get("session", {})
        
        # Extract facts from the message
        extracted_facts = await self.extract_facts(message.text, case_id)
        
        # Store extracted facts
        async with httpx.AsyncClient() as client:
            for fact in extracted_facts:
                try:
                    await client.post(
                        f"{self.session_hub_url}/cases/{case_id}/facts",
                        json={
                            "fact_type": fact["fact_type"],
                            "value": fact["value"],
                            "source": f"message_{message.timestamp}",
                            "confidence": fact["confidence"]
                        }
                    )
                except:
                    pass  # Continue processing even if fact storage fails
        
        # Assess completeness
        completeness = await self.assess_intake_completeness(case_id)
        
        # Generate follow-up questions if needed
        follow_up_questions = []
        if not completeness["is_complete"]:
            follow_up_questions = await self.generate_follow_up_questions(case_id, session_data)
        
        # Update session data
        session_updates = {
            "last_message": message.text,
            "extracted_facts_count": len(extracted_facts),
            "completeness_score": completeness["completeness_score"],
            "pending_questions": follow_up_questions
        }
        
        async with httpx.AsyncClient() as client:
            try:
                await client.put(
                    f"{self.session_hub_url}/cases/{case_id}/session",
                    json=session_updates
                )
            except:
                pass
        
        # Update case status if intake is complete
        if completeness["is_complete"]:
            async with httpx.AsyncClient() as client:
                try:
                    await client.put(
                        f"{self.session_hub_url}/cases/{case_id}/status",
                        json={"new_status": "intake_complete"}
                    )
                except:
                    pass
        
        return {
            "status": "processed",
            "extracted_facts": extracted_facts,
            "completeness": completeness,
            "follow_up_questions": follow_up_questions,
            "next_action": "intake_complete" if completeness["is_complete"] else "continue_intake"
        }

intake_engine = IntakeEngine()

@app.post("/process")
async def process_intake_message(routing_data: Dict[str, Any]):
    """Process an intake message and extract facts"""
    try:
        result = await intake_engine.process_message(routing_data)
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/health")
async def health_check():
    return {"status": "healthy"}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
