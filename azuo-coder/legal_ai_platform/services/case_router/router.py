from fastapi import FastAPI, HTTPException
import httpx
from typing import Dict, Any
from shared.models.case import NormalizedMessage, CaseStatus

app = FastAPI(title="Case Router Service")

class CaseRouter:
    def __init__(self):
        self.session_hub_url = "http://session_hub:8000"
        self.intake_engine_url = "http://intake_engine:8000"
        self.file_ingestion_url = "http://file_ingestion:8000"
        self.manual_review_url = "http://manual_review:8000"

    async def route_message(self, message: NormalizedMessage) -> Dict[str, Any]:
        """Route message to appropriate service based on content and case state"""

        # First, resolve the case through session hub
        async with httpx.AsyncClient() as client:
            try:
                response = await client.post(
                    f"{self.session_hub_url}/cases/resolve",
                    json=message.dict()
                )
                response.raise_for_status()
                case_info = response.json()
                case = case_info["case"]
                session = case_info["session"]

            except httpx.RequestError:
                return {
                    "action": "manual_review",
                    "reason": "session_hub_unavailable",
                    "message": message.dict()
                }

        # Route based on message content and case status
        if message.attachments:
            return {
                "action": "file_ingestion",
                "target_url": f"{self.file_ingestion_url}/process",
                "case_id": case["id"],
                "message": message.dict()
            }

        # Check case status for routing logic
        case_status = case.get("status", "pending")

        if case_status in ["pending", "in_progress"]:
            # Route to intake engine
            return {
                "action": "intake_processing",
                "target_url": f"{self.intake_engine_url}/process",
                "case_id": case["id"],
                "message": message.dict(),
                "session": session
            }

        elif case_status == "intake_complete":
            # Route to conversation logger or reasoning agent
            if self._needs_reasoning(message.text):
                return {
                    "action": "reasoning_analysis",
                    "target_url": "http://reasoning_agent:8000/analyze",
                    "case_id": case["id"],
                    "message": message.dict()
                }
            else:
                return {
                    "action": "conversation_log",
                    "target_url": f"{self.session_hub_url}/cases/{case['id']}/messages",
                    "case_id": case["id"],
                    "message": message.dict()
                }

        else:
            # For other statuses, log as conversation
            return {
                "action": "conversation_log",
                "target_url": f"{self.session_hub_url}/cases/{case['id']}/messages",
                "case_id": case["id"],
                "message": message.dict()
            }

    def _needs_reasoning(self, text: str) -> bool:
        """Determine if message needs reasoning analysis"""
        reasoning_keywords = [
            "inconsistent", "different", "changed", "mistake", "wrong",
            "actually", "correction", "update", "modify"
        ]
        return any(keyword in text.lower() for keyword in reasoning_keywords)

    async def execute_routing(self, routing_decision: Dict[str, Any]) -> Dict[str, Any]:
        """Execute the routing decision by calling the target service"""
        action = routing_decision["action"]

        if action == "manual_review":
            # Send to manual review queue
            return {"status": "queued_for_review", "routing": routing_decision}

        target_url = routing_decision.get("target_url")
        if not target_url:
            return {"status": "error", "message": "No target URL specified"}

        async with httpx.AsyncClient() as client:
            try:
                if action == "conversation_log":
                    # POST message to session hub
                    response = await client.post(target_url, json=routing_decision["message"])
                else:
                    # POST to processing service
                    response = await client.post(target_url, json=routing_decision)

                response.raise_for_status()
                return {
                    "status": "routed",
                    "action": action,
                    "response": response.json()
                }

            except httpx.RequestError as e:
                return {
                    "status": "routing_failed",
                    "action": action,
                    "error": str(e),
                    "fallback": "manual_review"
                }

router = CaseRouter()

@app.post("/route")
async def route_message_endpoint(message: NormalizedMessage):
    """Route a normalized message to the appropriate service"""
    try:
        routing_decision = await router.route_message(message)
        result = await router.execute_routing(routing_decision)
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/health")
async def health_check():
    return {"status": "healthy"}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)