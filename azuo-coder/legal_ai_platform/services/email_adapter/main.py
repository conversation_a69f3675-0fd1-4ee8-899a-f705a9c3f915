from fastapi import <PERSON><PERSON><PERSON>, Request, HTTPException, UploadFile, File
from typing import List, Optional
import httpx
import email
import re
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
import base64

from shared.models.case import NormalizedMessage, ChannelType

app = FastAPI(title="Email Adapter Service")

class EmailAdapter:
    def __init__(self):
        self.case_router_url = "http://case_router:8000"
        self.file_storage_url = "http://file_storage:8000"

    def extract_case_id_from_subject(self, subject: str) -> Optional[str]:
        """Extract case ID from email subject line"""
        # Look for patterns like [CASE-12345] or Case #12345
        patterns = [
            r'\[CASE-(\w+)\]',
            r'Case #(\w+)',
            r'Ref: (\w+)',
            r'ID: (\w+)'
        ]

        for pattern in patterns:
            match = re.search(pattern, subject, re.IGNORECASE)
            if match:
                return match.group(1)

        return None

    def parse_email_content(self, email_content: str) -> dict:
        """Parse email content and extract relevant information"""
        try:
            msg = email.message_from_string(email_content)

            # Extract basic headers
            from_addr = msg.get('From', '')
            to_addr = msg.get('To', '')
            subject = msg.get('Subject', '')
            date = msg.get('Date', '')

            # Extract case ID from subject
            case_id = self.extract_case_id_from_subject(subject)

            # Extract body text
            body_text = ""
            attachments = []

            if msg.is_multipart():
                for part in msg.walk():
                    content_type = part.get_content_type()
                    content_disposition = str(part.get('Content-Disposition', ''))

                    if content_type == "text/plain" and "attachment" not in content_disposition:
                        body_text += part.get_payload(decode=True).decode('utf-8', errors='ignore')
                    elif "attachment" in content_disposition:
                        filename = part.get_filename()
                        if filename:
                            attachments.append({
                                'filename': filename,
                                'content_type': content_type,
                                'content': part.get_payload(decode=True)
                            })
            else:
                body_text = msg.get_payload(decode=True).decode('utf-8', errors='ignore')

            return {
                'from': from_addr,
                'to': to_addr,
                'subject': subject,
                'date': date,
                'case_id': case_id,
                'body': body_text.strip(),
                'attachments': attachments
            }

        except Exception as e:
            raise HTTPException(status_code=400, detail=f"Failed to parse email: {str(e)}")

    async def store_attachments(self, attachments: List[dict], case_id: str = None) -> List[str]:
        """Store email attachments and return file paths"""
        stored_paths = []

        for attachment in attachments:
            try:
                # Upload attachment to file storage service
                async with httpx.AsyncClient() as client:
                    files = {
                        'file': (attachment['filename'], attachment['content'], attachment['content_type'])
                    }
                    data = {'case_id': case_id} if case_id else {}

                    response = await client.post(
                        f"{self.file_storage_url}/upload",
                        files=files,
                        data=data
                    )

                    if response.status_code == 200:
                        result = response.json()
                        stored_paths.append(result.get('file_path', ''))

            except Exception:
                # Continue processing even if attachment storage fails
                continue

        return stored_paths

    async def process_email(self, email_content: str) -> dict:
        """Process incoming email and route through system"""

        # Parse email content
        parsed_email = self.parse_email_content(email_content)

        # Store attachments if any
        attachment_paths = []
        if parsed_email['attachments']:
            attachment_paths = await self.store_attachments(
                parsed_email['attachments'],
                parsed_email['case_id']
            )

        # Create normalized message
        message = NormalizedMessage(
            case_id=parsed_email['case_id'],
            channel=ChannelType.EMAIL,
            text=parsed_email['body'],
            sender=parsed_email['from'],
            attachments=attachment_paths,
            metadata={
                'subject': parsed_email['subject'],
                'to': parsed_email['to'],
                'date': parsed_email['date'],
                'attachment_count': len(parsed_email['attachments'])
            }
        )

        # Route message through case router
        async with httpx.AsyncClient() as client:
            try:
                response = await client.post(
                    f"{self.case_router_url}/route",
                    json=message.dict()
                )
                response.raise_for_status()
                routing_result = response.json()

                return {
                    "status": "processed",
                    "parsed_email": {
                        "from": parsed_email['from'],
                        "subject": parsed_email['subject'],
                        "case_id": parsed_email['case_id'],
                        "attachment_count": len(parsed_email['attachments'])
                    },
                    "message": message.dict(),
                    "routing": routing_result
                }

            except httpx.RequestError as e:
                raise HTTPException(status_code=500, detail=f"Routing failed: {str(e)}")

email_adapter = EmailAdapter()

@app.post("/ingest")
async def handle_email(request: Request):
    """Handle incoming email webhook (e.g., from SendGrid, Mailgun, etc.)"""
    try:
        # Get raw email content
        body = await request.body()
        email_content = body.decode('utf-8')

        # Process the email
        result = await email_adapter.process_email(email_content)

        return result

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/ingest/raw")
async def handle_raw_email(email_content: str):
    """Handle raw email content directly"""
    try:
        result = await email_adapter.process_email(email_content)
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/ingest/file")
async def handle_email_file(file: UploadFile = File(...)):
    """Handle email uploaded as file"""
    try:
        email_content = await file.read()
        email_text = email_content.decode('utf-8')

        result = await email_adapter.process_email(email_text)
        return result

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/health")
async def health_check():
    return {"status": "healthy"}