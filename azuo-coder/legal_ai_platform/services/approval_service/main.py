from fastapi import Fast<PERSON>I, HTTPException
from typing import Dict, Any, List, Optional
import httpx
from datetime import datetime
from enum import Enum

app = FastAPI(title="Approval Service")

class ApprovalStatus(str, Enum):
    PENDING = "pending"
    APPROVED = "approved"
    REJECTED = "rejected"
    NEEDS_REVISION = "needs_revision"

class ApprovalType(str, Enum):
    DEMAND_LETTER = "demand_letter"
    COMPLAINT = "complaint"
    SETTLEMENT = "settlement"
    CASE_CLOSURE = "case_closure"

class ApprovalService:
    def __init__(self):
        self.session_hub_url = "http://session_hub:8000"
        self.notification_service_url = "http://notification_service:8000"
        
        # Approval workflow configuration
        self.approval_workflows = {
            ApprovalType.DEMAND_LETTER: {
                'required_approvers': ['senior_attorney'],
                'auto_approve_threshold': 50000,  # Auto-approve demands under $50k
                'review_criteria': [
                    'demand_amount_reasonable',
                    'facts_sufficient',
                    'legal_theory_sound'
                ]
            },
            ApprovalType.COMPLAINT: {
                'required_approvers': ['senior_attorney', 'managing_partner'],
                'auto_approve_threshold': 0,  # Never auto-approve complaints
                'review_criteria': [
                    'statute_of_limitations',
                    'venue_appropriate',
                    'causes_of_action_valid',
                    'damages_calculation'
                ]
            },
            ApprovalType.SETTLEMENT: {
                'required_approvers': ['senior_attorney'],
                'auto_approve_threshold': 25000,  # Auto-approve settlements under $25k
                'review_criteria': [
                    'settlement_amount_reasonable',
                    'client_consent',
                    'costs_covered'
                ]
            }
        }
    
    async def create_approval_request(self, case_id: str, approval_type: ApprovalType, 
                                    content: str, metadata: Dict[str, Any] = None) -> str:
        """Create a new approval request"""
        
        workflow = self.approval_workflows.get(approval_type)
        if not workflow:
            raise HTTPException(status_code=400, detail="Invalid approval type")
        
        # Check for auto-approval
        auto_approve = False
        if approval_type == ApprovalType.DEMAND_LETTER:
            demand_amount = metadata.get('demand_amount', 0) if metadata else 0
            if demand_amount < workflow['auto_approve_threshold']:
                auto_approve = True
        elif approval_type == ApprovalType.SETTLEMENT:
            settlement_amount = metadata.get('settlement_amount', 0) if metadata else 0
            if settlement_amount < workflow['auto_approve_threshold']:
                auto_approve = True
        
        approval_request = {
            'id': f"approval_{case_id}_{approval_type}_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
            'case_id': case_id,
            'approval_type': approval_type,
            'content': content,
            'metadata': metadata or {},
            'status': ApprovalStatus.APPROVED if auto_approve else ApprovalStatus.PENDING,
            'required_approvers': workflow['required_approvers'],
            'approvals': [],
            'created_at': datetime.now().isoformat(),
            'auto_approved': auto_approve
        }
        
        # Store approval request (in a real implementation, this would go to a database)
        # For now, we'll simulate storage
        
        if not auto_approve:
            # Send notifications to required approvers
            await self._notify_approvers(approval_request)
        
        return approval_request['id']
    
    async def _notify_approvers(self, approval_request: Dict[str, Any]):
        """Send notifications to required approvers"""
        for approver in approval_request['required_approvers']:
            try:
                async with httpx.AsyncClient() as client:
                    await client.post(
                        f"{self.notification_service_url}/notify",
                        json={
                            'recipient': approver,
                            'type': 'approval_request',
                            'subject': f"Approval Required: {approval_request['approval_type']}",
                            'message': f"Case {approval_request['case_id']} requires your approval for {approval_request['approval_type']}",
                            'metadata': {
                                'approval_id': approval_request['id'],
                                'case_id': approval_request['case_id']
                            }
                        }
                    )
            except:
                pass  # Continue even if notification fails
    
    async def submit_approval(self, approval_id: str, approver: str, 
                            decision: ApprovalStatus, comments: str = "") -> Dict[str, Any]:
        """Submit an approval decision"""
        
        # In a real implementation, retrieve approval request from database
        # For now, simulate retrieval
        approval_request = await self._get_approval_request(approval_id)
        
        if not approval_request:
            raise HTTPException(status_code=404, detail="Approval request not found")
        
        if approver not in approval_request['required_approvers']:
            raise HTTPException(status_code=403, detail="Not authorized to approve this request")
        
        # Check if already approved by this approver
        existing_approval = next(
            (a for a in approval_request['approvals'] if a['approver'] == approver), 
            None
        )
        
        if existing_approval:
            raise HTTPException(status_code=400, detail="Already approved by this approver")
        
        # Add approval
        approval_request['approvals'].append({
            'approver': approver,
            'decision': decision,
            'comments': comments,
            'timestamp': datetime.now().isoformat()
        })
        
        # Check if all required approvals are received
        approved_by = [a['approver'] for a in approval_request['approvals'] if a['decision'] == ApprovalStatus.APPROVED]
        rejected_by = [a['approver'] for a in approval_request['approvals'] if a['decision'] == ApprovalStatus.REJECTED]
        
        if rejected_by:
            approval_request['status'] = ApprovalStatus.REJECTED
        elif set(approved_by) >= set(approval_request['required_approvers']):
            approval_request['status'] = ApprovalStatus.APPROVED
        
        # Update case status if fully approved
        if approval_request['status'] == ApprovalStatus.APPROVED:
            await self._handle_approved_request(approval_request)
        
        return {
            'approval_id': approval_id,
            'status': approval_request['status'],
            'approvals_received': len(approval_request['approvals']),
            'approvals_required': len(approval_request['required_approvers'])
        }
    
    async def _get_approval_request(self, approval_id: str) -> Optional[Dict[str, Any]]:
        """Retrieve approval request (placeholder implementation)"""
        # In a real implementation, this would query the database
        # For now, return a mock approval request
        return {
            'id': approval_id,
            'case_id': 'case_123',
            'approval_type': ApprovalType.DEMAND_LETTER,
            'content': 'Mock demand letter content',
            'metadata': {'demand_amount': 75000},
            'status': ApprovalStatus.PENDING,
            'required_approvers': ['senior_attorney'],
            'approvals': [],
            'created_at': datetime.now().isoformat(),
            'auto_approved': False
        }
    
    async def _handle_approved_request(self, approval_request: Dict[str, Any]):
        """Handle fully approved request"""
        case_id = approval_request['case_id']
        approval_type = approval_request['approval_type']
        
        try:
            async with httpx.AsyncClient() as client:
                if approval_type == ApprovalType.DEMAND_LETTER:
                    # Update case status to indicate demand letter is approved
                    await client.put(
                        f"{self.session_hub_url}/cases/{case_id}/status",
                        json={'new_status': 'demand_approved'}
                    )
                elif approval_type == ApprovalType.COMPLAINT:
                    # Update case status to indicate complaint is approved for filing
                    await client.put(
                        f"{self.session_hub_url}/cases/{case_id}/status",
                        json={'new_status': 'complaint_approved'}
                    )
                elif approval_type == ApprovalType.SETTLEMENT:
                    # Update case status to indicate settlement is approved
                    await client.put(
                        f"{self.session_hub_url}/cases/{case_id}/status",
                        json={'new_status': 'settlement_approved'}
                    )
        except:
            pass  # Continue even if status update fails
    
    async def get_pending_approvals(self, approver: str) -> List[Dict[str, Any]]:
        """Get pending approvals for an approver"""
        # In a real implementation, this would query the database
        # For now, return mock data
        return [
            {
                'id': 'approval_case_123_demand_letter_20241201_143000',
                'case_id': 'case_123',
                'approval_type': 'demand_letter',
                'created_at': datetime.now().isoformat(),
                'metadata': {'demand_amount': 75000}
            }
        ]

approval_service = ApprovalService()

@app.post("/request")
async def create_approval_request_endpoint(
    case_id: str,
    approval_type: ApprovalType,
    content: str,
    metadata: Dict[str, Any] = None
):
    """Create a new approval request"""
    try:
        approval_id = await approval_service.create_approval_request(
            case_id, approval_type, content, metadata
        )
        return {"approval_id": approval_id, "status": "created"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/approve/{approval_id}")
async def submit_approval_endpoint(
    approval_id: str,
    approver: str,
    decision: ApprovalStatus,
    comments: str = ""
):
    """Submit an approval decision"""
    try:
        result = await approval_service.submit_approval(
            approval_id, approver, decision, comments
        )
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/pending/{approver}")
async def get_pending_approvals_endpoint(approver: str):
    """Get pending approvals for an approver"""
    try:
        approvals = await approval_service.get_pending_approvals(approver)
        return {"pending_approvals": approvals}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/health")
async def health_check():
    return {"status": "healthy"}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
