from fastapi import <PERSON><PERSON><PERSON>, HTTPException, UploadFile, File
from typing import Dict, Any, List
import httpx
import mimetypes
import tempfile
import os

from shared.models.case import DocumentType, Document

app = FastAPI(title="File Ingestion Service")

class FileIngestionService:
    def __init__(self):
        self.session_hub_url = "http://session_hub:8000"
        self.ocr_service_url = "http://ocr_service:8000"  # Placeholder
        self.document_storage_url = "http://document_storage:8000"
        
        # Document type classification patterns
        self.classification_patterns = {
            DocumentType.MEDICAL_RECORD: [
                'medical', 'hospital', 'doctor', 'physician', 'clinic', 'treatment',
                'diagnosis', 'prescription', 'x-ray', 'mri', 'ct scan', 'lab results'
            ],
            DocumentType.POLICE_REPORT: [
                'police', 'officer', 'incident report', 'accident report', 'citation',
                'traffic', 'collision', 'crash report', 'law enforcement'
            ],
            DocumentType.INSURANCE_DOCUMENT: [
                'insurance', 'policy', 'claim', 'coverage', 'deductible', 'premium',
                'adjuster', 'estimate', 'settlement', 'liability'
            ],
            DocumentType.CORRESPONDENCE: [
                'letter', 'email', 'correspondence', 'communication', 'notice',
                'demand', 'response', 'reply'
            ]
        }
    
    def classify_document(self, filename: str, ocr_text: str = "") -> DocumentType:
        """Classify document type based on filename and content"""
        
        # Combine filename and OCR text for classification
        text_to_analyze = f"{filename.lower()} {ocr_text.lower()}"
        
        # Score each document type
        scores = {}
        for doc_type, keywords in self.classification_patterns.items():
            score = sum(1 for keyword in keywords if keyword in text_to_analyze)
            scores[doc_type] = score
        
        # Return the type with highest score, or OTHER if no clear match
        if scores and max(scores.values()) > 0:
            return max(scores, key=scores.get)
        
        return DocumentType.OTHER
    
    async def perform_ocr(self, file_path: str, mime_type: str) -> str:
        """Perform OCR on document (placeholder implementation)"""
        
        # Check if file type supports OCR
        if not mime_type.startswith(('image/', 'application/pdf')):
            return ""
        
        try:
            # This would call an actual OCR service like:
            # - Tesseract
            # - Google Cloud Vision
            # - AWS Textract
            # - Azure Computer Vision
            
            # Placeholder implementation
            async with httpx.AsyncClient() as client:
                with open(file_path, 'rb') as f:
                    files = {'file': f}
                    response = await client.post(
                        f"{self.ocr_service_url}/extract-text",
                        files=files,
                        timeout=30.0
                    )
                    
                    if response.status_code == 200:
                        result = response.json()
                        return result.get('text', '')
                    
        except Exception:
            # OCR failed, continue without text
            pass
        
        return ""
    
    async def store_document(self, file_content: bytes, filename: str, case_id: str) -> str:
        """Store document in persistent storage and return path"""
        
        try:
            async with httpx.AsyncClient() as client:
                files = {
                    'file': (filename, file_content, mimetypes.guess_type(filename)[0])
                }
                data = {'case_id': case_id}
                
                response = await client.post(
                    f"{self.document_storage_url}/upload",
                    files=files,
                    data=data
                )
                
                if response.status_code == 200:
                    result = response.json()
                    return result.get('file_path', '')
                else:
                    raise HTTPException(status_code=500, detail="Document storage failed")
                    
        except httpx.RequestError:
            raise HTTPException(status_code=500, detail="Document storage service unavailable")
    
    async def process_file(self, routing_data: Dict[str, Any]) -> Dict[str, Any]:
        """Process uploaded file attachments"""
        
        case_id = routing_data["case_id"]
        message = routing_data["message"]
        attachments = message.get("attachments", [])
        
        processed_documents = []
        
        for attachment_url in attachments:
            try:
                # Download the attachment
                async with httpx.AsyncClient() as client:
                    response = await client.get(attachment_url)
                    response.raise_for_status()
                    file_content = response.content
                
                # Extract filename from URL or generate one
                filename = attachment_url.split('/')[-1] or f"attachment_{len(processed_documents)}"
                mime_type = mimetypes.guess_type(filename)[0] or 'application/octet-stream'
                
                # Store the document
                file_path = await self.store_document(file_content, filename, case_id)
                
                # Save to temporary file for OCR
                with tempfile.NamedTemporaryFile(delete=False, suffix=os.path.splitext(filename)[1]) as temp_file:
                    temp_file.write(file_content)
                    temp_file_path = temp_file.name
                
                try:
                    # Perform OCR
                    ocr_text = await self.perform_ocr(temp_file_path, mime_type)
                    
                    # Classify document
                    doc_type = self.classify_document(filename, ocr_text)
                    
                    # Create document record
                    document = Document(
                        case_id=case_id,
                        filename=filename,
                        document_type=doc_type,
                        file_path=file_path,
                        file_size=len(file_content),
                        mime_type=mime_type,
                        processed=True,
                        ocr_text=ocr_text,
                        metadata={
                            'original_url': attachment_url,
                            'classification_confidence': 0.8  # Placeholder
                        }
                    )
                    
                    # Store document record in database
                    async with httpx.AsyncClient() as client:
                        response = await client.post(
                            f"{self.session_hub_url}/cases/{case_id}/documents",
                            json=document.dict()
                        )
                        
                        if response.status_code == 200:
                            doc_result = response.json()
                            document.id = doc_result.get('document_id')
                    
                    processed_documents.append(document.dict())
                    
                finally:
                    # Clean up temp file
                    os.unlink(temp_file_path)
                    
            except Exception as e:
                # Log error but continue processing other attachments
                processed_documents.append({
                    'error': str(e),
                    'attachment_url': attachment_url,
                    'status': 'failed'
                })
        
        return {
            "status": "processed",
            "case_id": case_id,
            "processed_documents": processed_documents,
            "total_documents": len(attachments),
            "successful_documents": len([d for d in processed_documents if 'error' not in d])
        }

file_ingestion = FileIngestionService()

@app.post("/process")
async def process_file_attachments(routing_data: Dict[str, Any]):
    """Process file attachments from routed message"""
    try:
        result = await file_ingestion.process_file(routing_data)
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/upload")
async def upload_file_direct(case_id: str, file: UploadFile = File(...)):
    """Direct file upload endpoint"""
    try:
        file_content = await file.read()
        
        # Store the document
        file_path = await file_ingestion.store_document(file_content, file.filename, case_id)
        
        # Process the file
        routing_data = {
            "case_id": case_id,
            "message": {
                "attachments": [file_path]
            }
        }
        
        result = await file_ingestion.process_file(routing_data)
        return result
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/health")
async def health_check():
    return {"status": "healthy"}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
