from fastapi import <PERSON><PERSON><PERSON>, HTTPException
from typing import Dict, Any, List
import httpx
from datetime import datetime

app = FastAPI(title="Complaint Generator Service")

class ComplaintGenerator:
    def __init__(self):
        self.session_hub_url = "http://session_hub:8000"
        
        # Complaint template
        self.complaint_template = """
IN THE {court_name}
{court_location}

{plaintiff_name},                    )
                                     )
                    Plaintiff,       )    Case No. _______________
                                     )
v.                                   )
                                     )
{defendant_name},                    )
                                     )
                    Defendant.       )
_____________________________________)

COMPLAINT FOR {causes_of_action}

TO THE HONORABLE COURT:

COMES NOW Plaintiff, {plaintiff_name}, by and through undersigned counsel, and for their Complaint against Defendant, {defendant_name}, states as follows:

PARTIES

1. Plaintiff {plaintiff_name} is a resident of {plaintiff_location}.

2. Upon information and belief, Defendant {defendant_name} is a resident of {defendant_location}.

JURISDICTION AND VENUE

3. This Court has jurisdiction over this matter pursuant to {jurisdiction_basis}.

4. Venue is proper in this Court pursuant to {venue_basis}.

FACTUAL ALLEGATIONS

{factual_allegations}

COUNT I - NEGLIGENCE

{negligence_allegations}

COUNT II - {additional_count}

{additional_allegations}

DAMAGES

{damages_allegations}

WHEREFORE, Plaintiff demands judgment against Defendant for:

{prayer_for_relief}

                                    Respectfully submitted,

                                    _________________________
                                    {attorney_name}
                                    {attorney_bar_number}
                                    {law_firm_name}
                                    {law_firm_address}
                                    {attorney_phone}
                                    {attorney_email}
                                    Attorney for Plaintiff

VERIFICATION

I, {plaintiff_name}, being first duly sworn, depose and state that I am the Plaintiff in the above-entitled action; that I have read the foregoing Complaint and know the contents thereof; and that the same is true to my own knowledge, except as to those matters which are therein stated upon information and belief, and as to those matters, I believe them to be true.

                                    _________________________
                                    {plaintiff_name}

Subscribed and sworn to before me this _____ day of _________, 20__.

                                    _________________________
                                    Notary Public
"""
    
    async def get_case_data(self, case_id: str) -> Dict[str, Any]:
        """Retrieve comprehensive case data"""
        async with httpx.AsyncClient() as client:
            try:
                # Get case details
                case_response = await client.get(f"{self.session_hub_url}/cases/{case_id}")
                case_data = case_response.json() if case_response.status_code == 200 else {}
                
                # Get facts
                facts_response = await client.get(f"{self.session_hub_url}/cases/{case_id}/facts")
                facts = facts_response.json() if facts_response.status_code == 200 else []
                
                # Get timeline
                timeline_response = await client.get(f"{self.session_hub_url}/cases/{case_id}/timeline")
                timeline = timeline_response.json() if timeline_response.status_code == 200 else []
                
                return {
                    'case': case_data,
                    'facts': facts,
                    'timeline': timeline
                }
            except:
                return {'case': {}, 'facts': [], 'timeline': []}
    
    def extract_fact_value(self, facts: List[Dict], fact_type: str) -> str:
        """Extract value for a specific fact type"""
        for fact in facts:
            if fact.get('fact_type') == fact_type:
                return fact.get('value', '')
        return ''
    
    def generate_factual_allegations(self, facts: List[Dict], timeline: List[Dict]) -> str:
        """Generate factual allegations section"""
        allegations = []
        count = 5  # Starting paragraph number after parties/jurisdiction
        
        # Incident date and location
        incident_date = self.extract_fact_value(facts, 'incident_date')
        incident_location = self.extract_fact_value(facts, 'location')
        
        if incident_date and incident_location:
            allegations.append(f"{count}. On or about {incident_date}, at approximately [TIME], an incident occurred at {incident_location}.")
            count += 1
        
        # Incident description
        incident_desc = self.extract_fact_value(facts, 'incident_description')
        if incident_desc:
            allegations.append(f"{count}. {incident_desc}")
            count += 1
        
        # Vehicle information
        vehicle_type = self.extract_fact_value(facts, 'vehicle_type')
        if vehicle_type:
            allegations.append(f"{count}. At the time of the incident, Plaintiff was operating a {vehicle_type}.")
            count += 1
        
        # Other party information
        other_party = self.extract_fact_value(facts, 'other_party')
        if other_party:
            allegations.append(f"{count}. The incident involved {other_party}.")
            count += 1
        
        # Add timeline events as allegations
        for event in timeline[:3]:  # Limit to first 3 timeline events
            allegations.append(f"{count}. {event.get('description', '')}")
            count += 1
        
        return "\n\n".join(allegations)
    
    def generate_negligence_allegations(self, facts: List[Dict]) -> str:
        """Generate negligence count allegations"""
        allegations = []
        
        allegations.append("Plaintiff realleges and incorporates by reference each and every allegation contained in the preceding paragraphs as if fully set forth herein.")
        
        allegations.append("At all times material hereto, Defendant owed a duty to Plaintiff to exercise reasonable care in the operation of their motor vehicle.")
        
        allegations.append("Defendant breached their duty of care by, among other things:")
        
        # Add specific negligent acts based on facts
        negligent_acts = [
            "a) Failing to maintain a proper lookout;",
            "b) Failing to yield the right of way;",
            "c) Failing to maintain control of their vehicle;",
            "d) Operating their vehicle at an excessive speed;",
            "e) Failing to observe traffic control devices."
        ]
        
        allegations.extend(negligent_acts)
        
        allegations.append("As a direct and proximate result of Defendant's negligence, Plaintiff sustained serious injuries and damages as set forth herein.")
        
        return "\n\n".join(allegations)
    
    def generate_damages_allegations(self, facts: List[Dict], case_data: Dict) -> str:
        """Generate damages section"""
        damages = []
        
        damages.append("As a direct and proximate result of Defendant's negligence, Plaintiff has suffered and will continue to suffer damages including, but not limited to:")
        
        # Medical expenses
        medical_expenses = self.extract_fact_value(facts, 'medical_expenses')
        if medical_expenses:
            damages.append(f"a) Medical expenses in the amount of ${medical_expenses};")
        else:
            damages.append("a) Past and future medical expenses;")
        
        # Lost wages
        lost_wages = self.extract_fact_value(facts, 'lost_wages')
        if lost_wages:
            damages.append(f"b) Lost wages in the amount of ${lost_wages};")
        else:
            damages.append("b) Past and future lost wages and diminished earning capacity;")
        
        # Property damage
        property_damage = self.extract_fact_value(facts, 'property_damage')
        if property_damage:
            damages.append(f"c) Property damage in the amount of ${property_damage};")
        
        # General damages
        damages.extend([
            "d) Physical pain and suffering;",
            "e) Mental anguish and emotional distress;",
            "f) Loss of enjoyment of life;",
            "g) Permanent disability and disfigurement;",
            "h) Such other damages as may be proven at trial."
        ])
        
        return "\n".join(damages)
    
    def generate_prayer_for_relief(self, case_data: Dict) -> str:
        """Generate prayer for relief"""
        demand_amount = case_data.get('damage_estimate', 100000)
        
        prayer = [
            f"a) Compensatory damages in an amount to be determined at trial, but not less than ${demand_amount:,.0f};",
            "b) Pre-judgment and post-judgment interest as allowed by law;",
            "c) Costs of suit;",
            "d) Attorney's fees as allowed by law;",
            "e) Such other relief as this Court deems just and proper."
        ]
        
        return "\n".join(prayer)
    
    async def generate_complaint(self, case_id: str) -> Dict[str, Any]:
        """Generate a complaint for the case"""
        
        # Get case data
        case_data_full = await self.get_case_data(case_id)
        case_data = case_data_full['case']
        facts = case_data_full['facts']
        timeline = case_data_full['timeline']
        
        # Extract required information
        plaintiff_name = case_data.get('client_name', '[PLAINTIFF NAME]')
        defendant_name = self.extract_fact_value(facts, 'other_party') or '[DEFENDANT NAME]'
        
        # Generate content sections
        factual_allegations = self.generate_factual_allegations(facts, timeline)
        negligence_allegations = self.generate_negligence_allegations(facts)
        damages_allegations = self.generate_damages_allegations(facts, case_data)
        prayer_for_relief = self.generate_prayer_for_relief(case_data)
        
        # Fill template
        complaint = self.complaint_template.format(
            court_name="DISTRICT COURT",  # Default, should be configurable
            court_location="[COURT LOCATION]",
            plaintiff_name=plaintiff_name,
            defendant_name=defendant_name,
            causes_of_action="NEGLIGENCE",
            plaintiff_location="[PLAINTIFF LOCATION]",
            defendant_location="[DEFENDANT LOCATION]",
            jurisdiction_basis="[JURISDICTION BASIS]",
            venue_basis="[VENUE BASIS]",
            factual_allegations=factual_allegations,
            negligence_allegations=negligence_allegations,
            additional_count="[ADDITIONAL COUNT IF APPLICABLE]",
            additional_allegations="[ADDITIONAL ALLEGATIONS IF APPLICABLE]",
            damages_allegations=damages_allegations,
            prayer_for_relief=prayer_for_relief,
            attorney_name="[ATTORNEY NAME]",
            attorney_bar_number="[BAR NUMBER]",
            law_firm_name="[LAW FIRM NAME]",
            law_firm_address="[LAW FIRM ADDRESS]",
            attorney_phone="[ATTORNEY PHONE]",
            attorney_email="[ATTORNEY EMAIL]"
        )
        
        # Store complaint in case
        async with httpx.AsyncClient() as client:
            try:
                await client.put(
                    f"{self.session_hub_url}/cases/{case_id}",
                    json={'complaint_draft': complaint}
                )
            except:
                pass
        
        return {
            'case_id': case_id,
            'complaint': complaint,
            'generated_at': datetime.now().isoformat(),
            'status': 'draft'
        }

complaint_generator = ComplaintGenerator()

@app.post("/generate")
async def generate_complaint_endpoint(case_id: str):
    """Generate a complaint for a case"""
    try:
        result = await complaint_generator.generate_complaint(case_id)
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/health")
async def health_check():
    return {"status": "healthy"}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
