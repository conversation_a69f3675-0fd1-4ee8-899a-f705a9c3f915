from fastapi import FastAP<PERSON>, HTTPException
from typing import Dict, Any, List, Optional
import httpx
from datetime import datetime

app = FastAPI(title="Demand Generator Service")

class DemandGenerator:
    def __init__(self):
        self.session_hub_url = "http://session_hub:8000"
        self.llm_service_url = "http://llm_service:8000"  # Placeholder
        
        # Demand letter template
        self.demand_template = """
{date}

{insurance_company_name}
{insurance_company_address}

Re: Demand for Settlement
    Insured: {insured_name}
    Claimant: {claimant_name}
    Date of Loss: {incident_date}
    Claim Number: {claim_number}

Dear Claims Representative:

I represent {claimant_name} in connection with the {incident_type} that occurred on {incident_date} at {incident_location}.

FACTS OF THE INCIDENT:

{incident_description}

INJURIES AND TREATMENT:

{injury_description}

{medical_treatment_summary}

DAMAGES:

The damages sustained by my client include:

{damages_breakdown}

DEMAND:

Based on the facts set forth above, I hereby demand the sum of ${demand_amount} in full and final settlement of all claims arising from this incident.

This demand will remain open for thirty (30) days from the date of this letter. If we do not receive your response within this time frame, we will assume that you have rejected this demand and will proceed accordingly.

Please contact me immediately to discuss settlement of this matter.

Very truly yours,

{attorney_name}
{attorney_title}
{law_firm_name}

cc: {claimant_name}
"""
    
    async def get_case_data(self, case_id: str) -> Dict[str, Any]:
        """Retrieve comprehensive case data"""
        async with httpx.AsyncClient() as client:
            try:
                # Get case details
                case_response = await client.get(f"{self.session_hub_url}/cases/{case_id}")
                case_data = case_response.json() if case_response.status_code == 200 else {}
                
                # Get facts
                facts_response = await client.get(f"{self.session_hub_url}/cases/{case_id}/facts")
                facts = facts_response.json() if facts_response.status_code == 200 else []
                
                # Get documents
                docs_response = await client.get(f"{self.session_hub_url}/cases/{case_id}/documents")
                documents = docs_response.json() if docs_response.status_code == 200 else []
                
                return {
                    'case': case_data,
                    'facts': facts,
                    'documents': documents
                }
            except:
                return {'case': {}, 'facts': [], 'documents': []}
    
    def extract_fact_value(self, facts: List[Dict], fact_type: str) -> str:
        """Extract value for a specific fact type"""
        for fact in facts:
            if fact.get('fact_type') == fact_type:
                return fact.get('value', '')
        return ''
    
    def generate_incident_description(self, facts: List[Dict]) -> str:
        """Generate incident description from facts"""
        incident_desc = self.extract_fact_value(facts, 'incident_description')
        if incident_desc:
            return incident_desc
        
        # Build description from available facts
        location = self.extract_fact_value(facts, 'location')
        vehicle_type = self.extract_fact_value(facts, 'vehicle_type')
        other_party = self.extract_fact_value(facts, 'other_party')
        
        description_parts = []
        if location:
            description_parts.append(f"The incident occurred at {location}")
        if vehicle_type and other_party:
            description_parts.append(f"involving my client's {vehicle_type} and {other_party}")
        elif vehicle_type:
            description_parts.append(f"involving my client's {vehicle_type}")
        
        return ". ".join(description_parts) + "." if description_parts else "Details of the incident are being investigated."
    
    def generate_injury_description(self, facts: List[Dict]) -> str:
        """Generate injury description from facts"""
        injury_desc = self.extract_fact_value(facts, 'injury_description')
        if injury_desc:
            return injury_desc
        
        injury_type = self.extract_fact_value(facts, 'injury_type')
        if injury_type:
            return f"As a result of this incident, my client sustained {injury_type}."
        
        return "My client sustained injuries as a result of this incident."
    
    def generate_medical_treatment_summary(self, facts: List[Dict], documents: List[Dict]) -> str:
        """Generate medical treatment summary"""
        treatment_facts = [fact for fact in facts if 'medical' in fact.get('fact_type', '').lower() or 'treatment' in fact.get('fact_type', '').lower()]
        medical_docs = [doc for doc in documents if doc.get('document_type') == 'medical_record']
        
        summary_parts = []
        
        if treatment_facts:
            for fact in treatment_facts:
                summary_parts.append(f"• {fact.get('value', '')}")
        
        if medical_docs:
            summary_parts.append(f"• Medical records and documentation are attached ({len(medical_docs)} documents)")
        
        if not summary_parts:
            summary_parts.append("• Medical treatment details are being compiled")
        
        return "\n".join(summary_parts)
    
    def calculate_damages_breakdown(self, case_data: Dict, facts: List[Dict]) -> str:
        """Calculate and format damages breakdown"""
        damages = []
        
        # Medical expenses
        medical_expenses = self.extract_fact_value(facts, 'medical_expenses')
        if medical_expenses:
            damages.append(f"• Medical expenses: ${medical_expenses}")
        else:
            damages.append("• Medical expenses: To be determined upon completion of treatment")
        
        # Lost wages
        lost_wages = self.extract_fact_value(facts, 'lost_wages')
        if lost_wages:
            damages.append(f"• Lost wages: ${lost_wages}")
        
        # Property damage
        property_damage = self.extract_fact_value(facts, 'property_damage')
        if property_damage:
            damages.append(f"• Property damage: ${property_damage}")
        
        # Pain and suffering
        damages.append("• Pain and suffering")
        damages.append("• Mental anguish and emotional distress")
        damages.append("• Loss of enjoyment of life")
        
        return "\n".join(damages)
    
    def calculate_demand_amount(self, case_data: Dict, facts: List[Dict]) -> float:
        """Calculate demand amount based on case data"""
        
        # Start with damage estimate from case
        base_amount = case_data.get('damage_estimate', 25000)
        
        # Add specific damages if available
        medical_expenses = self.extract_fact_value(facts, 'medical_expenses')
        lost_wages = self.extract_fact_value(facts, 'lost_wages')
        property_damage = self.extract_fact_value(facts, 'property_damage')
        
        total_specials = 0
        try:
            if medical_expenses:
                total_specials += float(medical_expenses.replace('$', '').replace(',', ''))
            if lost_wages:
                total_specials += float(lost_wages.replace('$', '').replace(',', ''))
            if property_damage:
                total_specials += float(property_damage.replace('$', '').replace(',', ''))
        except:
            pass
        
        # Use higher of base estimate or calculated specials * multiplier
        calculated_amount = max(base_amount, total_specials * 3)  # 3x multiplier for pain/suffering
        
        # Round to nearest thousand
        return round(calculated_amount / 1000) * 1000
    
    async def generate_demand_letter(self, case_id: str) -> Dict[str, Any]:
        """Generate a demand letter for the case"""
        
        # Get case data
        case_data_full = await self.get_case_data(case_id)
        case_data = case_data_full['case']
        facts = case_data_full['facts']
        documents = case_data_full['documents']
        
        # Extract required information
        claimant_name = case_data.get('client_name', 'Client Name')
        incident_date = self.extract_fact_value(facts, 'incident_date') or case_data.get('incident_date', 'Date TBD')
        incident_location = self.extract_fact_value(facts, 'location') or case_data.get('incident_location', 'Location TBD')
        
        # Generate content sections
        incident_description = self.generate_incident_description(facts)
        injury_description = self.generate_injury_description(facts)
        medical_treatment_summary = self.generate_medical_treatment_summary(facts, documents)
        damages_breakdown = self.calculate_damages_breakdown(case_data, facts)
        demand_amount = self.calculate_demand_amount(case_data, facts)
        
        # Fill template
        demand_letter = self.demand_template.format(
            date=datetime.now().strftime("%B %d, %Y"),
            insurance_company_name="[Insurance Company Name]",
            insurance_company_address="[Insurance Company Address]",
            insured_name="[Insured Name]",
            claimant_name=claimant_name,
            incident_date=incident_date,
            claim_number="[Claim Number]",
            incident_type="motor vehicle accident",  # Default, could be extracted from facts
            incident_location=incident_location,
            incident_description=incident_description,
            injury_description=injury_description,
            medical_treatment_summary=medical_treatment_summary,
            damages_breakdown=damages_breakdown,
            demand_amount=f"{demand_amount:,.0f}",
            attorney_name="[Attorney Name]",
            attorney_title="Attorney for Claimant",
            law_firm_name="[Law Firm Name]"
        )
        
        # Store demand letter in case
        async with httpx.AsyncClient() as client:
            try:
                await client.put(
                    f"{self.session_hub_url}/cases/{case_id}",
                    json={'demand_letter': demand_letter}
                )
            except:
                pass
        
        return {
            'case_id': case_id,
            'demand_letter': demand_letter,
            'demand_amount': demand_amount,
            'generated_at': datetime.now().isoformat(),
            'status': 'draft'
        }

demand_generator = DemandGenerator()

@app.post("/generate")
async def generate_demand_letter_endpoint(case_id: str):
    """Generate a demand letter for a case"""
    try:
        result = await demand_generator.generate_demand_letter(case_id)
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/health")
async def health_check():
    return {"status": "healthy"}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
