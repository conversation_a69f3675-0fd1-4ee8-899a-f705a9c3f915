from fastapi import <PERSON><PERSON><PERSON>, Request, HTTPException, Form
from typing import Optional
import httpx
import base64
import tempfile
import os

from shared.models.case import NormalizedMessage, ChannelType

app = FastAPI(title="Voice Adapter Service")

class VoiceAdapter:
    def __init__(self):
        self.case_router_url = "http://case_router:8000"
        self.transcription_service_url = "http://transcription_service:8000"  # Placeholder
        
    async def transcribe_audio(self, audio_url: str) -> str:
        """Transcribe audio from URL to text"""
        try:
            # Download audio file
            async with httpx.AsyncClient() as client:
                audio_response = await client.get(audio_url)
                audio_response.raise_for_status()
                audio_data = audio_response.content
            
            # Save to temporary file
            with tempfile.NamedTemporaryFile(delete=False, suffix='.wav') as temp_file:
                temp_file.write(audio_data)
                temp_file_path = temp_file.name
            
            try:
                # Call transcription service (placeholder)
                transcription = await self._call_transcription_service(temp_file_path)
                return transcription
            finally:
                # Clean up temp file
                os.unlink(temp_file_path)
                
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"Transcription failed: {str(e)}")
    
    async def _call_transcription_service(self, audio_file_path: str) -> str:
        """Call external transcription service (placeholder)"""
        # This would integrate with services like:
        # - OpenAI Whisper
        # - Google Speech-to-Text
        # - AWS Transcribe
        # - Azure Speech Services
        
        # For now, return placeholder text
        return "This is a placeholder transcription. In a real implementation, this would contain the actual transcribed text from the audio file."
    
    async def process_twilio_webhook(self, request_data: dict) -> dict:
        """Process incoming Twilio voice webhook"""
        
        # Extract Twilio webhook data
        call_sid = request_data.get('CallSid')
        from_number = request_data.get('From')
        to_number = request_data.get('To')
        recording_url = request_data.get('RecordingUrl')
        
        if not recording_url:
            raise HTTPException(status_code=400, detail="No recording URL provided")
        
        # Transcribe the audio
        transcribed_text = await self.transcribe_audio(recording_url)
        
        # Create normalized message
        message = NormalizedMessage(
            channel=ChannelType.VOICE,
            text=transcribed_text,
            sender=from_number,
            metadata={
                'call_sid': call_sid,
                'to_number': to_number,
                'recording_url': recording_url,
                'transcription_confidence': 0.85  # Placeholder confidence
            }
        )
        
        # Route message through case router
        async with httpx.AsyncClient() as client:
            try:
                response = await client.post(
                    f"{self.case_router_url}/route",
                    json=message.dict()
                )
                response.raise_for_status()
                routing_result = response.json()
                
                return {
                    "status": "processed",
                    "transcription": transcribed_text,
                    "routing": routing_result,
                    "call_sid": call_sid
                }
                
            except httpx.RequestError as e:
                raise HTTPException(status_code=500, detail=f"Routing failed: {str(e)}")
    
    def generate_twiml_response(self, message: str = None) -> str:
        """Generate TwiML response for Twilio"""
        if message:
            return f"""<?xml version="1.0" encoding="UTF-8"?>
<Response>
    <Say voice="alice">{message}</Say>
    <Record timeout="30" transcribe="false" recordingStatusCallback="/voice/recording-complete"/>
    <Say voice="alice">Thank you. We have received your message and will process it shortly.</Say>
</Response>"""
        else:
            return """<?xml version="1.0" encoding="UTF-8"?>
<Response>
    <Say voice="alice">Thank you for calling our legal intake line. Please describe your legal matter after the beep, and we will get back to you shortly.</Say>
    <Record timeout="60" transcribe="false" recordingStatusCallback="/voice/recording-complete"/>
    <Say voice="alice">Thank you. We have received your message and will review it promptly.</Say>
</Response>"""

voice_adapter = VoiceAdapter()

@app.post("/voice/webhook")
async def twilio_voice_webhook(request: Request):
    """Handle incoming Twilio voice webhook"""
    try:
        # Parse form data from Twilio
        form_data = await request.form()
        request_data = dict(form_data)
        
        # Generate initial TwiML response
        twiml = voice_adapter.generate_twiml_response()
        
        return {
            "content": twiml,
            "media_type": "application/xml"
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/voice/recording-complete")
async def recording_complete_webhook(request: Request):
    """Handle recording completion webhook from Twilio"""
    try:
        form_data = await request.form()
        request_data = dict(form_data)
        
        # Process the completed recording
        result = await voice_adapter.process_twilio_webhook(request_data)
        
        return result
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/voice/transcribe")
async def transcribe_audio_endpoint(audio_url: str):
    """Direct transcription endpoint"""
    try:
        transcription = await voice_adapter.transcribe_audio(audio_url)
        return {"transcription": transcription}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/health")
async def health_check():
    return {"status": "healthy"}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
