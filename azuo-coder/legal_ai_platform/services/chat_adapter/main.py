from fastapi import <PERSON><PERSON><PERSON>, Request, HTTPException
from typing import Optional, List
import httpx
import json

from shared.models.case import NormalizedMessage, ChannelType

app = FastAPI(title="Chat/SMS Adapter Service")

class ChatAdapter:
    def __init__(self):
        self.case_router_url = "http://case_router:8000"
    
    async def process_sms_webhook(self, request_data: dict) -> dict:
        """Process incoming Twilio SMS webhook"""
        
        # Extract Twilio SMS data
        message_sid = request_data.get('MessageSid')
        from_number = request_data.get('From')
        to_number = request_data.get('To')
        body = request_data.get('Body', '')
        media_urls = []
        
        # Handle media attachments
        num_media = int(request_data.get('NumMedia', 0))
        for i in range(num_media):
            media_url = request_data.get(f'MediaUrl{i}')
            if media_url:
                media_urls.append(media_url)
        
        # Create normalized message
        message = NormalizedMessage(
            channel=ChannelType.SMS,
            text=body,
            sender=from_number,
            attachments=media_urls,
            metadata={
                'message_sid': message_sid,
                'to_number': to_number,
                'num_media': num_media
            }
        )
        
        # Route message
        return await self._route_message(message)
    
    async def process_whatsapp_webhook(self, request_data: dict) -> dict:
        """Process incoming WhatsApp webhook"""
        
        # Extract WhatsApp data (Twilio WhatsApp API format)
        message_sid = request_data.get('MessageSid')
        from_number = request_data.get('From')  # Format: whatsapp:+1234567890
        to_number = request_data.get('To')
        body = request_data.get('Body', '')
        media_urls = []
        
        # Handle media attachments
        num_media = int(request_data.get('NumMedia', 0))
        for i in range(num_media):
            media_url = request_data.get(f'MediaUrl{i}')
            if media_url:
                media_urls.append(media_url)
        
        # Clean phone number (remove whatsapp: prefix)
        clean_from = from_number.replace('whatsapp:', '') if from_number else None
        
        # Create normalized message
        message = NormalizedMessage(
            channel=ChannelType.WHATSAPP,
            text=body,
            sender=clean_from,
            attachments=media_urls,
            metadata={
                'message_sid': message_sid,
                'to_number': to_number,
                'num_media': num_media,
                'original_from': from_number
            }
        )
        
        # Route message
        return await self._route_message(message)
    
    async def process_web_chat(self, chat_data: dict) -> dict:
        """Process web chat message"""
        
        # Extract web chat data
        session_id = chat_data.get('session_id')
        user_id = chat_data.get('user_id')
        message_text = chat_data.get('message', '')
        attachments = chat_data.get('attachments', [])
        
        # Create normalized message
        message = NormalizedMessage(
            channel=ChannelType.CHAT,
            text=message_text,
            sender=user_id,
            attachments=attachments,
            metadata={
                'session_id': session_id,
                'user_id': user_id
            }
        )
        
        # Route message
        return await self._route_message(message)
    
    async def _route_message(self, message: NormalizedMessage) -> dict:
        """Route message through case router"""
        async with httpx.AsyncClient() as client:
            try:
                response = await client.post(
                    f"{self.case_router_url}/route",
                    json=message.dict()
                )
                response.raise_for_status()
                routing_result = response.json()
                
                return {
                    "status": "processed",
                    "message": message.dict(),
                    "routing": routing_result
                }
                
            except httpx.RequestError as e:
                raise HTTPException(status_code=500, detail=f"Routing failed: {str(e)}")
    
    def generate_sms_response(self, message: str) -> str:
        """Generate TwiML response for SMS"""
        return f"""<?xml version="1.0" encoding="UTF-8"?>
<Response>
    <Message>{message}</Message>
</Response>"""
    
    def generate_whatsapp_response(self, message: str) -> str:
        """Generate TwiML response for WhatsApp"""
        return f"""<?xml version="1.0" encoding="UTF-8"?>
<Response>
    <Message>{message}</Message>
</Response>"""

chat_adapter = ChatAdapter()

@app.post("/sms/webhook")
async def twilio_sms_webhook(request: Request):
    """Handle incoming Twilio SMS webhook"""
    try:
        # Parse form data from Twilio
        form_data = await request.form()
        request_data = dict(form_data)
        
        # Process the SMS
        result = await chat_adapter.process_sms_webhook(request_data)
        
        # Generate response message
        response_message = "Thank you for your message. We have received it and will respond shortly."
        twiml = chat_adapter.generate_sms_response(response_message)
        
        return {
            "content": twiml,
            "media_type": "application/xml",
            "processing_result": result
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/whatsapp/webhook")
async def twilio_whatsapp_webhook(request: Request):
    """Handle incoming Twilio WhatsApp webhook"""
    try:
        # Parse form data from Twilio
        form_data = await request.form()
        request_data = dict(form_data)
        
        # Process the WhatsApp message
        result = await chat_adapter.process_whatsapp_webhook(request_data)
        
        # Generate response message
        response_message = "Thank you for contacting us via WhatsApp. We have received your message and will respond shortly."
        twiml = chat_adapter.generate_whatsapp_response(response_message)
        
        return {
            "content": twiml,
            "media_type": "application/xml",
            "processing_result": result
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/chat/message")
async def web_chat_message(chat_data: dict):
    """Handle web chat message"""
    try:
        result = await chat_adapter.process_web_chat(chat_data)
        
        return {
            "status": "received",
            "message": "Thank you for your message. We are processing it now.",
            "processing_result": result
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/health")
async def health_check():
    return {"status": "healthy"}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
