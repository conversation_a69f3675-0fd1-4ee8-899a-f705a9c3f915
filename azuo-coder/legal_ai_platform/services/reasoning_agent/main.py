from fastapi import FastAP<PERSON>, HTTPException
from typing import Dict, Any, List, Optional
import httpx
from datetime import datetime, timedelta
import re

from shared.models.case import CaseFact, TimelineEvent

app = FastAPI(title="Reasoning Agent Service")

class ReasoningAgent:
    def __init__(self):
        self.session_hub_url = "http://session_hub:8000"
        self.llm_service_url = "http://llm_service:8000"  # Placeholder
        
        # Damage estimation factors
        self.damage_factors = {
            'injury_severity': {
                'minor': 1.0,
                'moderate': 2.5,
                'severe': 5.0,
                'catastrophic': 10.0
            },
            'medical_treatment': {
                'none': 0.5,
                'first_aid': 1.0,
                'emergency_room': 2.0,
                'hospitalization': 4.0,
                'surgery': 6.0,
                'ongoing_treatment': 3.0
            },
            'lost_wages': {
                'none': 1.0,
                'days': 1.5,
                'weeks': 2.0,
                'months': 3.0,
                'permanent': 5.0
            }
        }
    
    async def get_case_facts(self, case_id: str) -> List[CaseFact]:
        """Retrieve all facts for a case"""
        async with httpx.AsyncClient() as client:
            try:
                response = await client.get(f"{self.session_hub_url}/cases/{case_id}/facts")
                if response.status_code == 200:
                    facts_data = response.json()
                    return [CaseFact(**fact) for fact in facts_data]
                return []
            except:
                return []
    
    async def get_case_timeline(self, case_id: str) -> List[TimelineEvent]:
        """Retrieve timeline events for a case"""
        async with httpx.AsyncClient() as client:
            try:
                response = await client.get(f"{self.session_hub_url}/cases/{case_id}/timeline")
                if response.status_code == 200:
                    timeline_data = response.json()
                    return [TimelineEvent(**event) for event in timeline_data]
                return []
            except:
                return []
    
    def detect_inconsistencies(self, facts: List[CaseFact]) -> List[Dict[str, Any]]:
        """Detect inconsistencies in case facts"""
        inconsistencies = []
        
        # Group facts by type
        fact_groups = {}
        for fact in facts:
            if fact.fact_type not in fact_groups:
                fact_groups[fact.fact_type] = []
            fact_groups[fact.fact_type].append(fact)
        
        # Check for conflicting values within each fact type
        for fact_type, fact_list in fact_groups.items():
            if len(fact_list) > 1:
                values = [fact.value.lower().strip() for fact in fact_list]
                unique_values = set(values)
                
                if len(unique_values) > 1:
                    inconsistencies.append({
                        'type': 'conflicting_facts',
                        'fact_type': fact_type,
                        'conflicting_values': list(unique_values),
                        'sources': [fact.source for fact in fact_list],
                        'confidence_scores': [fact.confidence for fact in fact_list]
                    })
        
        # Check for date inconsistencies
        incident_dates = [fact for fact in facts if fact.fact_type == 'incident_date']
        treatment_dates = [fact for fact in facts if 'treatment_date' in fact.fact_type]
        
        if incident_dates and treatment_dates:
            try:
                incident_date = datetime.fromisoformat(incident_dates[0].value)
                for treatment_fact in treatment_dates:
                    treatment_date = datetime.fromisoformat(treatment_fact.value)
                    if treatment_date < incident_date:
                        inconsistencies.append({
                            'type': 'temporal_inconsistency',
                            'description': 'Treatment date before incident date',
                            'incident_date': incident_dates[0].value,
                            'treatment_date': treatment_fact.value
                        })
            except:
                pass  # Date parsing failed
        
        return inconsistencies
    
    def construct_timeline(self, facts: List[CaseFact], events: List[TimelineEvent]) -> List[Dict[str, Any]]:
        """Construct a coherent timeline from facts and events"""
        timeline_items = []
        
        # Add existing timeline events
        for event in events:
            timeline_items.append({
                'date': event.event_date,
                'type': 'event',
                'description': event.description,
                'source': event.source,
                'confidence': event.confidence
            })
        
        # Extract date-related facts and add to timeline
        date_facts = [fact for fact in facts if 'date' in fact.fact_type.lower()]
        
        for fact in date_facts:
            try:
                # Try to parse the date
                date_value = self._parse_date(fact.value)
                if date_value:
                    timeline_items.append({
                        'date': date_value,
                        'type': 'fact',
                        'description': f"{fact.fact_type}: {fact.value}",
                        'source': fact.source,
                        'confidence': fact.confidence
                    })
            except:
                continue
        
        # Sort timeline by date
        timeline_items.sort(key=lambda x: x['date'])
        
        return timeline_items
    
    def _parse_date(self, date_string: str) -> Optional[datetime]:
        """Parse various date formats"""
        date_patterns = [
            r'(\d{4}-\d{2}-\d{2})',  # YYYY-MM-DD
            r'(\d{2}/\d{2}/\d{4})',  # MM/DD/YYYY
            r'(\d{1,2}/\d{1,2}/\d{2,4})',  # M/D/YY or MM/DD/YYYY
        ]
        
        for pattern in date_patterns:
            match = re.search(pattern, date_string)
            if match:
                try:
                    date_str = match.group(1)
                    if '/' in date_str:
                        # Handle MM/DD/YYYY format
                        parts = date_str.split('/')
                        if len(parts[2]) == 2:
                            parts[2] = '20' + parts[2]  # Assume 20xx for 2-digit years
                        return datetime(int(parts[2]), int(parts[0]), int(parts[1]))
                    else:
                        # Handle YYYY-MM-DD format
                        return datetime.fromisoformat(date_str)
                except:
                    continue
        
        return None
    
    def estimate_damages(self, facts: List[CaseFact]) -> Dict[str, Any]:
        """Estimate potential damages based on case facts"""
        
        # Extract relevant facts
        injury_facts = [fact for fact in facts if 'injury' in fact.fact_type.lower()]
        medical_facts = [fact for fact in facts if 'medical' in fact.fact_type.lower() or 'treatment' in fact.fact_type.lower()]
        wage_facts = [fact for fact in facts if 'wage' in fact.fact_type.lower() or 'work' in fact.fact_type.lower()]
        
        # Base damage estimate
        base_estimate = 5000  # Minimum case value
        
        # Injury severity multiplier
        injury_multiplier = 1.0
        for fact in injury_facts:
            value_lower = fact.value.lower()
            if any(word in value_lower for word in ['severe', 'serious', 'major']):
                injury_multiplier = max(injury_multiplier, self.damage_factors['injury_severity']['severe'])
            elif any(word in value_lower for word in ['moderate', 'significant']):
                injury_multiplier = max(injury_multiplier, self.damage_factors['injury_severity']['moderate'])
            elif any(word in value_lower for word in ['minor', 'slight']):
                injury_multiplier = max(injury_multiplier, self.damage_factors['injury_severity']['minor'])
        
        # Medical treatment multiplier
        medical_multiplier = 1.0
        for fact in medical_facts:
            value_lower = fact.value.lower()
            if any(word in value_lower for word in ['surgery', 'operation']):
                medical_multiplier = max(medical_multiplier, self.damage_factors['medical_treatment']['surgery'])
            elif any(word in value_lower for word in ['hospital', 'admitted']):
                medical_multiplier = max(medical_multiplier, self.damage_factors['medical_treatment']['hospitalization'])
            elif any(word in value_lower for word in ['emergency', 'er']):
                medical_multiplier = max(medical_multiplier, self.damage_factors['medical_treatment']['emergency_room'])
        
        # Calculate estimated range
        low_estimate = base_estimate * injury_multiplier * medical_multiplier * 0.7
        high_estimate = base_estimate * injury_multiplier * medical_multiplier * 1.5
        
        return {
            'low_estimate': round(low_estimate, 2),
            'high_estimate': round(high_estimate, 2),
            'factors': {
                'injury_multiplier': injury_multiplier,
                'medical_multiplier': medical_multiplier,
                'base_estimate': base_estimate
            },
            'confidence': 0.6  # Moderate confidence for algorithmic estimation
        }
    
    async def analyze_case(self, case_id: str) -> Dict[str, Any]:
        """Perform comprehensive case analysis"""
        
        # Get case data
        facts = await self.get_case_facts(case_id)
        timeline_events = await self.get_case_timeline(case_id)
        
        # Perform analysis
        inconsistencies = self.detect_inconsistencies(facts)
        timeline = self.construct_timeline(facts, timeline_events)
        damage_estimate = self.estimate_damages(facts)
        
        # Generate flags for case
        flags = []
        if inconsistencies:
            flags.append(f"Found {len(inconsistencies)} inconsistencies")
        
        if damage_estimate['confidence'] < 0.5:
            flags.append("Low confidence in damage estimate")
        
        if len(facts) < 5:
            flags.append("Insufficient case information")
        
        # Update case with analysis results
        async with httpx.AsyncClient() as client:
            try:
                # Update damage estimate
                await client.put(
                    f"{self.session_hub_url}/cases/{case_id}",
                    json={
                        'damage_estimate': damage_estimate['high_estimate'],
                        'flags': flags
                    }
                )
                
                # Store timeline events
                for item in timeline:
                    if item['type'] == 'fact' and item not in timeline_events:
                        await client.post(
                            f"{self.session_hub_url}/cases/{case_id}/timeline",
                            json={
                                'event_date': item['date'].isoformat(),
                                'event_type': 'extracted_fact',
                                'description': item['description'],
                                'source': item['source']
                            }
                        )
            except:
                pass  # Continue even if updates fail
        
        return {
            'case_id': case_id,
            'inconsistencies': inconsistencies,
            'timeline': [
                {**item, 'date': item['date'].isoformat()} 
                for item in timeline
            ],
            'damage_estimate': damage_estimate,
            'flags': flags,
            'analysis_confidence': 0.75
        }

reasoning_agent = ReasoningAgent()

@app.post("/analyze")
async def analyze_case_endpoint(routing_data: Dict[str, Any]):
    """Analyze a case for inconsistencies and generate insights"""
    try:
        case_id = routing_data.get('case_id')
        if not case_id:
            raise HTTPException(status_code=400, detail="Case ID required")
        
        result = await reasoning_agent.analyze_case(case_id)
        return result
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/health")
async def health_check():
    return {"status": "healthy"}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
