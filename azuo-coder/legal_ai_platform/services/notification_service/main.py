from fastapi import <PERSON><PERSON><PERSON>, HTTPException
from typing import Dict, Any, List, Optional
import httpx
from datetime import datetime
from enum import Enum

app = FastAPI(title="Notification Service")

class NotificationType(str, Enum):
    EMAIL = "email"
    SMS = "sms"
    SLACK = "slack"
    WEBHOOK = "webhook"

class NotificationPriority(str, Enum):
    LOW = "low"
    NORMAL = "normal"
    HIGH = "high"
    URGENT = "urgent"

class NotificationService:
    def __init__(self):
        # External service configurations
        self.email_config = {
            'smtp_server': 'smtp.gmail.com',
            'smtp_port': 587,
            'username': '<EMAIL>',
            'password': 'your-app-password'
        }
        
        self.sms_config = {
            'twilio_account_sid': 'your-twilio-sid',
            'twilio_auth_token': 'your-twilio-token',
            'from_number': '+**********'
        }
        
        self.slack_config = {
            'webhook_url': 'https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK'
        }
        
        # User preferences (in production, this would be in a database)
        self.user_preferences = {
            'admin': {
                'email': '<EMAIL>',
                'phone': '+**********',
                'slack_user': '@admin',
                'notification_types': [NotificationType.EMAIL, NotificationType.SLACK],
                'urgent_types': [NotificationType.EMAIL, NotificationType.SMS, NotificationType.SLACK]
            },
            'senior_attorney': {
                'email': '<EMAIL>',
                'phone': '+**********',
                'slack_user': '@senior',
                'notification_types': [NotificationType.EMAIL],
                'urgent_types': [NotificationType.EMAIL, NotificationType.SMS]
            },
            'managing_partner': {
                'email': '<EMAIL>',
                'phone': '+1234567892',
                'slack_user': '@partner',
                'notification_types': [NotificationType.EMAIL, NotificationType.SLACK],
                'urgent_types': [NotificationType.EMAIL, NotificationType.SMS, NotificationType.SLACK]
            }
        }
    
    async def send_email(self, to_email: str, subject: str, message: str) -> bool:
        """Send email notification"""
        try:
            # In a real implementation, this would use SMTP or email service API
            # For now, simulate email sending
            print(f"EMAIL TO: {to_email}")
            print(f"SUBJECT: {subject}")
            print(f"MESSAGE: {message}")
            print("---")
            return True
        except Exception as e:
            print(f"Email sending failed: {e}")
            return False
    
    async def send_sms(self, to_phone: str, message: str) -> bool:
        """Send SMS notification"""
        try:
            # In a real implementation, this would use Twilio API
            # For now, simulate SMS sending
            print(f"SMS TO: {to_phone}")
            print(f"MESSAGE: {message}")
            print("---")
            return True
        except Exception as e:
            print(f"SMS sending failed: {e}")
            return False
    
    async def send_slack(self, channel_or_user: str, message: str) -> bool:
        """Send Slack notification"""
        try:
            # In a real implementation, this would use Slack API
            # For now, simulate Slack sending
            print(f"SLACK TO: {channel_or_user}")
            print(f"MESSAGE: {message}")
            print("---")
            return True
        except Exception as e:
            print(f"Slack sending failed: {e}")
            return False
    
    async def send_webhook(self, webhook_url: str, payload: Dict[str, Any]) -> bool:
        """Send webhook notification"""
        try:
            async with httpx.AsyncClient() as client:
                response = await client.post(webhook_url, json=payload)
                return response.status_code < 400
        except Exception as e:
            print(f"Webhook sending failed: {e}")
            return False
    
    def get_user_preferences(self, user_id: str) -> Optional[Dict[str, Any]]:
        """Get notification preferences for a user"""
        return self.user_preferences.get(user_id)
    
    async def send_notification(self, recipient: str, notification_type: str, 
                              subject: str, message: str, priority: NotificationPriority = NotificationPriority.NORMAL,
                              metadata: Dict[str, Any] = None) -> Dict[str, Any]:
        """Send a notification to a recipient"""
        
        user_prefs = self.get_user_preferences(recipient)
        if not user_prefs:
            return {"status": "error", "message": "User not found"}
        
        # Determine which notification types to use based on priority
        if priority == NotificationPriority.URGENT:
            types_to_send = user_prefs.get('urgent_types', [])
        else:
            types_to_send = user_prefs.get('notification_types', [])
        
        results = {}
        
        # Send email
        if NotificationType.EMAIL in types_to_send:
            email_success = await self.send_email(user_prefs['email'], subject, message)
            results['email'] = email_success
        
        # Send SMS
        if NotificationType.SMS in types_to_send:
            sms_message = f"{subject}: {message[:140]}..."  # Truncate for SMS
            sms_success = await self.send_sms(user_prefs['phone'], sms_message)
            results['sms'] = sms_success
        
        # Send Slack
        if NotificationType.SLACK in types_to_send:
            slack_message = f"*{subject}*\n{message}"
            slack_success = await self.send_slack(user_prefs['slack_user'], slack_message)
            results['slack'] = slack_success
        
        # Log notification
        notification_log = {
            'recipient': recipient,
            'subject': subject,
            'message': message,
            'priority': priority,
            'types_sent': list(results.keys()),
            'results': results,
            'timestamp': datetime.now().isoformat(),
            'metadata': metadata or {}
        }
        
        return {
            "status": "sent",
            "notification_log": notification_log,
            "success_count": sum(1 for success in results.values() if success),
            "total_attempts": len(results)
        }
    
    async def send_case_notification(self, case_id: str, event_type: str, 
                                   message: str, priority: NotificationPriority = NotificationPriority.NORMAL) -> Dict[str, Any]:
        """Send case-related notification to relevant parties"""
        
        # Determine recipients based on event type
        recipients = []
        
        if event_type in ['new_case', 'intake_complete']:
            recipients = ['admin', 'senior_attorney']
        elif event_type in ['approval_required', 'demand_ready']:
            recipients = ['senior_attorney', 'managing_partner']
        elif event_type in ['case_filed', 'settlement_reached']:
            recipients = ['admin', 'senior_attorney', 'managing_partner']
        else:
            recipients = ['admin']
        
        results = {}
        
        for recipient in recipients:
            subject = f"Case {case_id}: {event_type.replace('_', ' ').title()}"
            result = await self.send_notification(
                recipient, 
                'multi',  # Use multiple notification types
                subject, 
                message, 
                priority,
                {'case_id': case_id, 'event_type': event_type}
            )
            results[recipient] = result
        
        return {
            "case_id": case_id,
            "event_type": event_type,
            "recipients_notified": len(recipients),
            "results": results
        }
    
    async def send_bulk_notification(self, recipients: List[str], subject: str, 
                                   message: str, priority: NotificationPriority = NotificationPriority.NORMAL) -> Dict[str, Any]:
        """Send notification to multiple recipients"""
        
        results = {}
        
        for recipient in recipients:
            result = await self.send_notification(recipient, 'multi', subject, message, priority)
            results[recipient] = result
        
        return {
            "recipients_count": len(recipients),
            "results": results,
            "success_count": sum(1 for r in results.values() if r.get('status') == 'sent')
        }

notification_service = NotificationService()

@app.post("/notify")
async def send_notification_endpoint(notification_data: Dict[str, Any]):
    """Send a notification"""
    try:
        recipient = notification_data.get('recipient')
        notification_type = notification_data.get('type', 'multi')
        subject = notification_data.get('subject', 'Notification')
        message = notification_data.get('message', '')
        priority = NotificationPriority(notification_data.get('priority', 'normal'))
        metadata = notification_data.get('metadata', {})
        
        result = await notification_service.send_notification(
            recipient, notification_type, subject, message, priority, metadata
        )
        return result
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/notify/case")
async def send_case_notification_endpoint(case_notification: Dict[str, Any]):
    """Send case-related notification"""
    try:
        case_id = case_notification.get('case_id')
        event_type = case_notification.get('event_type')
        message = case_notification.get('message')
        priority = NotificationPriority(case_notification.get('priority', 'normal'))
        
        result = await notification_service.send_case_notification(
            case_id, event_type, message, priority
        )
        return result
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/notify/bulk")
async def send_bulk_notification_endpoint(bulk_notification: Dict[str, Any]):
    """Send bulk notification"""
    try:
        recipients = bulk_notification.get('recipients', [])
        subject = bulk_notification.get('subject', 'Notification')
        message = bulk_notification.get('message', '')
        priority = NotificationPriority(bulk_notification.get('priority', 'normal'))
        
        result = await notification_service.send_bulk_notification(
            recipients, subject, message, priority
        )
        return result
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/health")
async def health_check():
    return {"status": "healthy"}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
