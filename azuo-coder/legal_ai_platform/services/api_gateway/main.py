from fastapi import <PERSON><PERSON><PERSON>, HTTPException, Depends, Request
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from typing import Dict, Any, Optional
import httpx
import jwt
from datetime import datetime, timedelta

app = FastAPI(title="Legal AI Platform API Gateway")

# Security
security = HTTPBearer()

class APIGateway:
    def __init__(self):
        self.services = {
            'session_hub': 'http://session_hub:8000',
            'case_router': 'http://case_router:8000',
            'intake_engine': 'http://intake_engine:8000',
            'voice_adapter': 'http://voice_adapter:8000',
            'chat_adapter': 'http://chat_adapter:8000',
            'email_adapter': 'http://email_adapter:8000',
            'file_ingestion': 'http://file_ingestion:8000',
            'reasoning_agent': 'http://reasoning_agent:8000',
            'demand_generator': 'http://demand_generator:8000',
            'approval_service': 'http://approval_service:8000',
            'complaint_generator': 'http://complaint_generator:8000',
            'notification_service': 'http://notification_service:8000'
        }
        
        # JWT secret (in production, this should be from environment variables)
        self.jwt_secret = "your-secret-key"
        
        # Rate limiting (simplified)
        self.rate_limits = {}
    
    def verify_token(self, credentials: HTTPAuthorizationCredentials) -> Dict[str, Any]:
        """Verify JWT token"""
        try:
            payload = jwt.decode(credentials.credentials, self.jwt_secret, algorithms=["HS256"])
            return payload
        except jwt.ExpiredSignatureError:
            raise HTTPException(status_code=401, detail="Token expired")
        except jwt.InvalidTokenError:
            raise HTTPException(status_code=401, detail="Invalid token")
    
    def check_rate_limit(self, client_ip: str, endpoint: str) -> bool:
        """Simple rate limiting check"""
        key = f"{client_ip}:{endpoint}"
        now = datetime.now()
        
        if key not in self.rate_limits:
            self.rate_limits[key] = []
        
        # Remove old requests (older than 1 minute)
        self.rate_limits[key] = [
            req_time for req_time in self.rate_limits[key] 
            if now - req_time < timedelta(minutes=1)
        ]
        
        # Check if under limit (100 requests per minute)
        if len(self.rate_limits[key]) >= 100:
            return False
        
        self.rate_limits[key].append(now)
        return True
    
    async def proxy_request(self, service: str, path: str, method: str, 
                          data: Any = None, params: Dict = None) -> Dict[str, Any]:
        """Proxy request to internal service"""
        
        if service not in self.services:
            raise HTTPException(status_code=404, detail="Service not found")
        
        service_url = self.services[service]
        full_url = f"{service_url}{path}"
        
        async with httpx.AsyncClient() as client:
            try:
                if method.upper() == "GET":
                    response = await client.get(full_url, params=params)
                elif method.upper() == "POST":
                    response = await client.post(full_url, json=data, params=params)
                elif method.upper() == "PUT":
                    response = await client.put(full_url, json=data, params=params)
                elif method.upper() == "DELETE":
                    response = await client.delete(full_url, params=params)
                else:
                    raise HTTPException(status_code=405, detail="Method not allowed")
                
                return {
                    "status_code": response.status_code,
                    "data": response.json() if response.status_code < 400 else None,
                    "error": response.text if response.status_code >= 400 else None
                }
                
            except httpx.RequestError as e:
                raise HTTPException(status_code=503, detail=f"Service unavailable: {str(e)}")

gateway = APIGateway()

# Authentication dependency
async def get_current_user(credentials: HTTPAuthorizationCredentials = Depends(security)):
    return gateway.verify_token(credentials)

# Rate limiting dependency
async def check_rate_limit(request: Request):
    client_ip = request.client.host
    endpoint = request.url.path
    
    if not gateway.check_rate_limit(client_ip, endpoint):
        raise HTTPException(status_code=429, detail="Rate limit exceeded")
    
    return True

# Public endpoints (no authentication required)
@app.post("/webhooks/voice")
async def voice_webhook(request: Request, _: bool = Depends(check_rate_limit)):
    """Twilio voice webhook"""
    form_data = await request.form()
    result = await gateway.proxy_request("voice_adapter", "/voice/webhook", "POST", dict(form_data))
    return result["data"]

@app.post("/webhooks/sms")
async def sms_webhook(request: Request, _: bool = Depends(check_rate_limit)):
    """Twilio SMS webhook"""
    form_data = await request.form()
    result = await gateway.proxy_request("chat_adapter", "/sms/webhook", "POST", dict(form_data))
    return result["data"]

@app.post("/webhooks/whatsapp")
async def whatsapp_webhook(request: Request, _: bool = Depends(check_rate_limit)):
    """Twilio WhatsApp webhook"""
    form_data = await request.form()
    result = await gateway.proxy_request("chat_adapter", "/whatsapp/webhook", "POST", dict(form_data))
    return result["data"]

@app.post("/webhooks/email")
async def email_webhook(request: Request, _: bool = Depends(check_rate_limit)):
    """Email ingestion webhook"""
    body = await request.body()
    result = await gateway.proxy_request("email_adapter", "/ingest", "POST", body.decode())
    return result["data"]

# Authenticated endpoints
@app.get("/cases/{case_id}")
async def get_case(case_id: str, user: Dict = Depends(get_current_user), _: bool = Depends(check_rate_limit)):
    """Get case details"""
    result = await gateway.proxy_request("session_hub", f"/cases/{case_id}", "GET")
    if result["status_code"] != 200:
        raise HTTPException(status_code=result["status_code"], detail=result["error"])
    return result["data"]

@app.post("/cases/{case_id}/facts")
async def add_case_fact(case_id: str, fact_data: Dict[str, Any], 
                       user: Dict = Depends(get_current_user), _: bool = Depends(check_rate_limit)):
    """Add fact to case"""
    result = await gateway.proxy_request("session_hub", f"/cases/{case_id}/facts", "POST", fact_data)
    if result["status_code"] != 200:
        raise HTTPException(status_code=result["status_code"], detail=result["error"])
    return result["data"]

@app.post("/cases/{case_id}/analyze")
async def analyze_case(case_id: str, user: Dict = Depends(get_current_user), _: bool = Depends(check_rate_limit)):
    """Trigger case analysis"""
    result = await gateway.proxy_request("reasoning_agent", "/analyze", "POST", {"case_id": case_id})
    if result["status_code"] != 200:
        raise HTTPException(status_code=result["status_code"], detail=result["error"])
    return result["data"]

@app.post("/cases/{case_id}/demand")
async def generate_demand(case_id: str, user: Dict = Depends(get_current_user), _: bool = Depends(check_rate_limit)):
    """Generate demand letter"""
    result = await gateway.proxy_request("demand_generator", "/generate", "POST", {"case_id": case_id})
    if result["status_code"] != 200:
        raise HTTPException(status_code=result["status_code"], detail=result["error"])
    return result["data"]

@app.post("/approvals")
async def create_approval(approval_data: Dict[str, Any], 
                         user: Dict = Depends(get_current_user), _: bool = Depends(check_rate_limit)):
    """Create approval request"""
    result = await gateway.proxy_request("approval_service", "/request", "POST", approval_data)
    if result["status_code"] != 200:
        raise HTTPException(status_code=result["status_code"], detail=result["error"])
    return result["data"]

@app.post("/approvals/{approval_id}/approve")
async def submit_approval(approval_id: str, approval_data: Dict[str, Any],
                         user: Dict = Depends(get_current_user), _: bool = Depends(check_rate_limit)):
    """Submit approval decision"""
    result = await gateway.proxy_request("approval_service", f"/approve/{approval_id}", "POST", approval_data)
    if result["status_code"] != 200:
        raise HTTPException(status_code=result["status_code"], detail=result["error"])
    return result["data"]

@app.get("/approvals/pending")
async def get_pending_approvals(user: Dict = Depends(get_current_user), _: bool = Depends(check_rate_limit)):
    """Get pending approvals for user"""
    approver = user.get("username", "unknown")
    result = await gateway.proxy_request("approval_service", f"/pending/{approver}", "GET")
    if result["status_code"] != 200:
        raise HTTPException(status_code=result["status_code"], detail=result["error"])
    return result["data"]

# Authentication endpoint
@app.post("/auth/login")
async def login(credentials: Dict[str, str], _: bool = Depends(check_rate_limit)):
    """Authenticate user and return JWT token"""
    username = credentials.get("username")
    password = credentials.get("password")
    
    # In a real implementation, verify credentials against database
    if username == "admin" and password == "password":  # Placeholder
        payload = {
            "username": username,
            "role": "attorney",
            "exp": datetime.utcnow() + timedelta(hours=24)
        }
        token = jwt.encode(payload, gateway.jwt_secret, algorithm="HS256")
        return {"access_token": token, "token_type": "bearer"}
    else:
        raise HTTPException(status_code=401, detail="Invalid credentials")

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {"status": "healthy", "timestamp": datetime.now().isoformat()}

@app.get("/services/status")
async def services_status(user: Dict = Depends(get_current_user)):
    """Check status of all services"""
    status = {}
    
    for service_name, service_url in gateway.services.items():
        try:
            async with httpx.AsyncClient() as client:
                response = await client.get(f"{service_url}/health", timeout=5.0)
                status[service_name] = {
                    "status": "healthy" if response.status_code == 200 else "unhealthy",
                    "response_time": response.elapsed.total_seconds()
                }
        except:
            status[service_name] = {"status": "unreachable", "response_time": None}
    
    return status

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
