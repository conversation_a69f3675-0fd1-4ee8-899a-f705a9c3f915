from fastapi import <PERSON><PERSON><PERSON>, HTTPException, Depends
from typing import Optional, Dict, Any
import uuid
from datetime import datetime

from shared.models.case import Case, CaseStatus, CaseFact, TimelineEvent, NormalizedMessage
from shared.storage.interfaces import CaseRepository, SessionCache

app = FastAPI(title="Session Hub Service")

# Dependency injection placeholders - would be replaced with actual implementations
async def get_case_repository() -> CaseRepository:
    # This would return actual implementation
    raise NotImplementedError("Case repository not implemented")

async def get_session_cache() -> SessionCache:
    # This would return actual implementation  
    raise NotImplementedError("Session cache not implemented")

class SessionHub:
    def __init__(self, case_repo: CaseRepository, session_cache: SessionCache):
        self.case_repo = case_repo
        self.session_cache = session_cache
    
    async def get_or_create_case(self, message: NormalizedMessage) -> Case:
        """Get existing case or create new one based on message"""
        
        # Try to find existing case
        if message.case_id:
            case = await self.case_repo.get_case(message.case_id)
            if case:
                return case
        
        # Try to find by client info
        if message.sender:
            # Extract phone/email from sender
            if "@" in message.sender:
                case = await self.case_repo.find_case_by_client_info(email=message.sender)
            else:
                case = await self.case_repo.find_case_by_client_info(phone=message.sender)
            
            if case:
                return case
        
        # Create new case
        new_case = Case(
            id=str(uuid.uuid4()),
            status=CaseStatus.PENDING,
            client_phone=message.sender if message.sender and "@" not in message.sender else None,
            client_email=message.sender if message.sender and "@" in message.sender else None
        )
        
        case_id = await self.case_repo.create_case(new_case)
        new_case.id = case_id
        return new_case
    
    async def update_case_status(self, case_id: str, new_status: CaseStatus) -> bool:
        """Update case status with FSM validation"""
        case = await self.case_repo.get_case(case_id)
        if not case:
            return False
        
        # FSM validation
        valid_transitions = {
            CaseStatus.PENDING: [CaseStatus.IN_PROGRESS],
            CaseStatus.IN_PROGRESS: [CaseStatus.INTAKE_COMPLETE, CaseStatus.PENDING],
            CaseStatus.INTAKE_COMPLETE: [CaseStatus.DRAFT_READY, CaseStatus.IN_PROGRESS],
            CaseStatus.DRAFT_READY: [CaseStatus.UNDER_REVIEW, CaseStatus.INTAKE_COMPLETE],
            CaseStatus.UNDER_REVIEW: [CaseStatus.APPROVED, CaseStatus.DRAFT_READY],
            CaseStatus.APPROVED: [CaseStatus.FILED, CaseStatus.UNDER_REVIEW],
            CaseStatus.FILED: [CaseStatus.CLOSED],
            CaseStatus.CLOSED: []
        }
        
        if new_status not in valid_transitions.get(case.status, []):
            return False
        
        case.status = new_status
        case.updated_at = datetime.utcnow()
        return await self.case_repo.update_case(case)
    
    async def add_fact(self, case_id: str, fact_type: str, value: str, 
                      source: str, confidence: float = 1.0) -> str:
        """Add a fact to the case"""
        fact = CaseFact(
            id=str(uuid.uuid4()),
            case_id=case_id,
            fact_type=fact_type,
            value=value,
            source=source,
            confidence=confidence
        )
        return await self.case_repo.add_fact(fact)
    
    async def add_timeline_event(self, case_id: str, event_date: datetime,
                               event_type: str, description: str, source: str) -> str:
        """Add a timeline event to the case"""
        event = TimelineEvent(
            id=str(uuid.uuid4()),
            case_id=case_id,
            event_date=event_date,
            event_type=event_type,
            description=description,
            source=source
        )
        return await self.case_repo.add_timeline_event(event)
    
    async def get_session_data(self, case_id: str) -> Dict[str, Any]:
        """Get session data for a case"""
        session_data = await self.session_cache.get_session(case_id)
        if not session_data:
            # Initialize session data
            session_data = {
                "case_id": case_id,
                "current_step": "intake",
                "pending_questions": [],
                "last_activity": datetime.utcnow().isoformat()
            }
            await self.session_cache.set_session(case_id, session_data)
        return session_data
    
    async def update_session_data(self, case_id: str, updates: Dict[str, Any]) -> bool:
        """Update session data"""
        updates["last_activity"] = datetime.utcnow().isoformat()
        return await self.session_cache.update_session(case_id, updates)

# API Endpoints
@app.post("/cases/resolve")
async def resolve_case(
    message: NormalizedMessage,
    session_hub: SessionHub = Depends(lambda: SessionHub(get_case_repository(), get_session_cache()))
):
    """Resolve case from message and return case info"""
    try:
        case = await session_hub.get_or_create_case(message)
        session_data = await session_hub.get_session_data(case.id)
        
        return {
            "case": case.dict(),
            "session": session_data
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.put("/cases/{case_id}/status")
async def update_status(
    case_id: str,
    new_status: CaseStatus,
    session_hub: SessionHub = Depends(lambda: SessionHub(get_case_repository(), get_session_cache()))
):
    """Update case status"""
    success = await session_hub.update_case_status(case_id, new_status)
    if not success:
        raise HTTPException(status_code=400, detail="Invalid status transition")
    return {"status": "updated"}

@app.post("/cases/{case_id}/facts")
async def add_fact(
    case_id: str,
    fact_type: str,
    value: str,
    source: str,
    confidence: float = 1.0,
    session_hub: SessionHub = Depends(lambda: SessionHub(get_case_repository(), get_session_cache()))
):
    """Add a fact to the case"""
    fact_id = await session_hub.add_fact(case_id, fact_type, value, source, confidence)
    return {"fact_id": fact_id}

@app.get("/cases/{case_id}/session")
async def get_session(
    case_id: str,
    session_hub: SessionHub = Depends(lambda: SessionHub(get_case_repository(), get_session_cache()))
):
    """Get session data for a case"""
    session_data = await session_hub.get_session_data(case_id)
    return session_data

@app.put("/cases/{case_id}/session")
async def update_session(
    case_id: str,
    updates: Dict[str, Any],
    session_hub: SessionHub = Depends(lambda: SessionHub(get_case_repository(), get_session_cache()))
):
    """Update session data"""
    success = await session_hub.update_session_data(case_id, updates)
    if not success:
        raise HTTPException(status_code=500, detail="Failed to update session")
    return {"status": "updated"}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
