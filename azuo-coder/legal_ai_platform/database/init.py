#!/usr/bin/env python3
"""
Database initialization script for Legal AI Platform
"""

import asyncio
import asyncpg
import os
from pathlib import Path

async def init_database():
    """Initialize the database with schema"""
    
    # Database connection parameters
    database_url = os.getenv("DATABASE_URL", "postgresql://postgres:password@localhost:5432/legal_ai")
    
    try:
        # Connect to database
        conn = await asyncpg.connect(database_url)
        print("Connected to database successfully")
        
        # Read schema file
        schema_path = Path(__file__).parent / "schema.sql"
        with open(schema_path, 'r') as f:
            schema_sql = f.read()
        
        # Execute schema
        await conn.execute(schema_sql)
        print("Database schema created successfully")
        
        # Insert sample data
        await insert_sample_data(conn)
        print("Sample data inserted successfully")
        
        await conn.close()
        print("Database initialization completed")
        
    except Exception as e:
        print(f"Database initialization failed: {e}")
        raise

async def insert_sample_data(conn):
    """Insert sample data for testing"""
    
    # Sample case
    case_id = await conn.fetchval("""
        INSERT INTO cases (client_name, client_phone, client_email, status, incident_description)
        VALUES ($1, $2, $3, $4, $5)
        RETURNING id
    """, "John Doe", "+1234567890", "<EMAIL>", "pending", 
        "Car accident at intersection of Main St and 5th Ave")
    
    # Sample facts
    await conn.execute("""
        INSERT INTO case_facts (case_id, fact_type, value, confidence, source)
        VALUES 
            ($1, 'incident_date', '2024-01-15', 0.9, 'client_message'),
            ($1, 'injury_type', 'whiplash', 0.8, 'client_message'),
            ($1, 'location', 'Main St and 5th Ave', 0.95, 'client_message')
    """, case_id)
    
    # Sample timeline event
    await conn.execute("""
        INSERT INTO timeline_events (case_id, event_date, event_type, description, source, confidence)
        VALUES ($1, '2024-01-15 14:30:00', 'incident', 'Motor vehicle accident occurred', 'client_report', 0.9)
    """, case_id)
    
    # Sample message
    await conn.execute("""
        INSERT INTO messages (case_id, channel, text, sender)
        VALUES ($1, 'sms', 'I was in a car accident yesterday and need legal help', '+1234567890')
    """, case_id)
    
    print(f"Sample case created with ID: {case_id}")

if __name__ == "__main__":
    asyncio.run(init_database())
