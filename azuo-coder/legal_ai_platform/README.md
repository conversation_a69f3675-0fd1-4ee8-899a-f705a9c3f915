# Legal AI Platform

A comprehensive AI-powered legal intake and case management system that handles multi-channel client communications (voice, SMS, email, chat) and automates legal document generation.

## Architecture Overview

The system follows a microservices architecture with the following components:

### Client Channels
- **Voice Adapter**: Handles Twilio voice calls with transcription
- **Chat/SMS Adapter**: Processes SMS, WhatsApp, and web chat messages
- **Email Adapter**: Ingests emails with attachment processing

### Core Services
- **API Gateway**: Unified entry point with authentication and rate limiting
- **Session Hub**: Central case session management with FSM state tracking
- **Case Router**: Intelligent message routing based on content and case state
- **Intake Engine**: LLM-powered fact extraction and follow-up generation

### AI Services
- **Reasoning Agent**: Timeline construction and inconsistency detection
- **Demand Generator**: Automated demand letter creation
- **Complaint Generator**: Court-ready filing generation
- **File Ingestion**: OCR processing and document classification

### Workflow Services
- **Approval Service**: Multi-attorney approval workflows
- **Notification Service**: Multi-channel notifications (email, SMS, Slack)

### Storage Layer
- **PostgreSQL**: Structured case data, facts, timeline, documents
- **Redis**: Session caching and recent activity
- **S3 (Interface)**: Document and audio storage

## Quick Start

### Prerequisites
- Docker and Docker Compose
- Python 3.11+ (for local development)

### Running the System

1. **Clone and navigate to the project:**
   ```bash
   git clone <repository-url>
   cd legal_ai_platform
   ```

2. **Start all services:**
   ```bash
   docker-compose up -d
   ```

3. **Check service health:**
   ```bash
   curl http://localhost:8000/health
   curl http://localhost:8000/services/status
   ```

### Service Endpoints

| Service | Port | Health Check |
|---------|------|--------------|
| API Gateway | 8000 | http://localhost:8000/health |
| Session Hub | 8001 | http://localhost:8001/health |
| Case Router | 8002 | http://localhost:8002/health |
| Intake Engine | 8003 | http://localhost:8003/health |
| Voice Adapter | 8004 | http://localhost:8004/health |
| Chat Adapter | 8005 | http://localhost:8005/health |
| Email Adapter | 8006 | http://localhost:8006/health |
| File Ingestion | 8007 | http://localhost:8007/health |
| Reasoning Agent | 8008 | http://localhost:8008/health |
| Demand Generator | 8009 | http://localhost:8009/health |
| Complaint Generator | 8010 | http://localhost:8010/health |
| Approval Service | 8011 | http://localhost:8011/health |
| Notification Service | 8012 | http://localhost:8012/health |

## API Usage

### Authentication

Get an access token:
```bash
curl -X POST http://localhost:8000/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username": "admin", "password": "password"}'
```

Use the token in subsequent requests:
```bash
curl -H "Authorization: Bearer <token>" http://localhost:8000/cases/123
```

### Key API Endpoints

#### Case Management
```bash
# Get case details
GET /cases/{case_id}

# Add fact to case
POST /cases/{case_id}/facts
{
  "fact_type": "incident_date",
  "value": "2024-01-15",
  "source": "client_message",
  "confidence": 0.9
}

# Analyze case
POST /cases/{case_id}/analyze

# Generate demand letter
POST /cases/{case_id}/demand
```

#### Webhooks (No authentication required)
```bash
# Twilio voice webhook
POST /webhooks/voice

# Twilio SMS webhook
POST /webhooks/sms

# Email ingestion webhook
POST /webhooks/email
```

## Message Flow

1. **Client Contact**: Voice call, SMS, email, or chat message received
2. **Channel Adapter**: Normalizes message format and extracts attachments
3. **Case Router**: Routes to appropriate service based on content and case state
4. **Session Hub**: Resolves or creates case, manages session state
5. **Processing**:
   - **Intake Engine**: Extracts facts, generates follow-ups
   - **File Ingestion**: Processes attachments with OCR
   - **Reasoning Agent**: Analyzes for inconsistencies
6. **Document Generation**: Creates demand letters or complaints when ready
7. **Approval Workflow**: Routes documents through attorney approval
8. **Notifications**: Keeps stakeholders informed of case progress

## Development

### Local Development Setup

1. **Install dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

2. **Start storage services:**
   ```bash
   docker-compose up postgres redis -d
   ```

3. **Run individual services:**
   ```bash
   cd services/session_hub
   uvicorn main:app --reload --port 8001
   ```

### Adding New Services

1. Create service directory under `services/`
2. Implement FastAPI application with health check endpoint
3. Add service to `docker-compose.yml`
4. Update API Gateway routing if needed

### Storage Interface Implementation

The system defines storage interfaces in `shared/storage/interfaces.py`. To implement actual storage:

1. **PostgreSQL Implementation**: Implement `CaseRepository` interface
2. **S3 Implementation**: Implement `DocumentStorage` interface
3. **Redis Implementation**: Implement `SessionCache` interface

Example PostgreSQL implementation:
```python
from shared.storage.interfaces import CaseRepository
import asyncpg

class PostgresCaseRepository(CaseRepository):
    async def create_case(self, case: Case) -> str:
        # Implementation using asyncpg
        pass
```

## Configuration

### Environment Variables

Set these in your environment or `.env` file:

```bash
# Database
DATABASE_URL=postgresql://postgres:password@localhost:5432/legal_ai
REDIS_URL=redis://localhost:6379

# JWT
JWT_SECRET=your-secret-key

# Twilio
TWILIO_ACCOUNT_SID=your-twilio-sid
TWILIO_AUTH_TOKEN=your-twilio-token

# Email
SMTP_SERVER=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your-app-password

# Slack
SLACK_WEBHOOK_URL=https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK
```

## Testing

### Manual Testing

1. **Test voice webhook:**
   ```bash
   curl -X POST http://localhost:8004/voice/webhook \
     -d "CallSid=test123&From=%2B**********&RecordingUrl=http://example.com/recording.wav"
   ```

2. **Test SMS webhook:**
   ```bash
   curl -X POST http://localhost:8005/sms/webhook \
     -d "MessageSid=test123&From=%2B**********&Body=I was in a car accident"
   ```

3. **Test case creation:**
   ```bash
   curl -X POST http://localhost:8000/cases \
     -H "Authorization: Bearer <token>" \
     -H "Content-Type: application/json" \
     -d '{"client_name": "John Doe", "client_phone": "+**********"}'
   ```

### Integration Testing

The system includes comprehensive integration tests. Run with:
```bash
pytest tests/
```

## Monitoring

### Health Checks

All services expose `/health` endpoints. The API Gateway provides a consolidated status at `/services/status`.

### Logging

Services log to stdout. In production, configure log aggregation:
```bash
docker-compose logs -f session_hub
```

## Production Deployment

### Security Considerations

1. **Change default passwords** in `docker-compose.yml`
2. **Set strong JWT secret** in environment variables
3. **Configure HTTPS** with reverse proxy (nginx/traefik)
4. **Implement proper authentication** beyond placeholder
5. **Set up monitoring** and alerting
6. **Configure backup** for PostgreSQL data

### Scaling

- **Horizontal scaling**: Run multiple instances behind load balancer
- **Database scaling**: Use read replicas for PostgreSQL
- **Cache scaling**: Use Redis cluster for high availability
- **Storage scaling**: Implement S3 or compatible object storage

## Troubleshooting

### Common Issues

1. **Service won't start**: Check Docker logs and port conflicts
2. **Database connection failed**: Verify PostgreSQL is running and credentials
3. **Authentication errors**: Check JWT secret configuration
4. **Webhook failures**: Verify external service URLs and credentials

### Debug Mode

Enable debug logging:
```bash
export LOG_LEVEL=DEBUG
docker-compose up
```

## Contributing

1. Fork the repository
2. Create feature branch
3. Implement changes with tests
4. Submit pull request

## License

[Your License Here]
