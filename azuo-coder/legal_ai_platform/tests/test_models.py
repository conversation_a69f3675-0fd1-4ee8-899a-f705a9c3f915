import pytest
from datetime import datetime
from shared.models.case import (
    Case, CaseFact, TimelineEvent, Document, NormalizedMessage,
    CaseStatus, ChannelType, DocumentType
)

def test_normalized_message_creation():
    """Test creating a normalized message"""
    message = NormalizedMessage(
        channel=ChannelType.EMAIL,
        text="I was in a car accident",
        sender="<EMAIL>"
    )
    
    assert message.channel == ChannelType.EMAIL
    assert message.text == "I was in a car accident"
    assert message.sender == "<EMAIL>"
    assert message.case_id is None
    assert message.attachments == []
    assert isinstance(message.timestamp, datetime)

def test_case_creation():
    """Test creating a case"""
    case = Case(
        client_name="<PERSON>",
        client_email="<EMAIL>",
        client_phone="+1234567890"
    )
    
    assert case.client_name == "<PERSON>"
    assert case.client_email == "<EMAIL>"
    assert case.client_phone == "+1234567890"
    assert case.status == CaseStatus.PENDING
    assert case.facts == []
    assert case.timeline == []
    assert case.documents == []

def test_case_fact_creation():
    """Test creating a case fact"""
    fact = CaseFact(
        case_id="case_123",
        fact_type="incident_date",
        value="2024-01-15",
        confidence=0.9,
        source="client_message"
    )
    
    assert fact.case_id == "case_123"
    assert fact.fact_type == "incident_date"
    assert fact.value == "2024-01-15"
    assert fact.confidence == 0.9
    assert fact.source == "client_message"
    assert fact.verified is False

def test_timeline_event_creation():
    """Test creating a timeline event"""
    event = TimelineEvent(
        case_id="case_123",
        event_date=datetime(2024, 1, 15, 10, 30),
        event_type="incident",
        description="Car accident occurred",
        source="police_report",
        confidence=1.0
    )
    
    assert event.case_id == "case_123"
    assert event.event_date == datetime(2024, 1, 15, 10, 30)
    assert event.event_type == "incident"
    assert event.description == "Car accident occurred"
    assert event.source == "police_report"
    assert event.confidence == 1.0

def test_document_creation():
    """Test creating a document"""
    document = Document(
        case_id="case_123",
        filename="police_report.pdf",
        document_type=DocumentType.POLICE_REPORT,
        file_path="/documents/case_123/police_report.pdf",
        file_size=1024000,
        mime_type="application/pdf"
    )
    
    assert document.case_id == "case_123"
    assert document.filename == "police_report.pdf"
    assert document.document_type == DocumentType.POLICE_REPORT
    assert document.file_path == "/documents/case_123/police_report.pdf"
    assert document.file_size == 1024000
    assert document.mime_type == "application/pdf"
    assert document.processed is False
    assert document.ocr_text is None

def test_case_status_enum():
    """Test case status enum values"""
    assert CaseStatus.PENDING == "pending"
    assert CaseStatus.IN_PROGRESS == "in_progress"
    assert CaseStatus.INTAKE_COMPLETE == "intake_complete"
    assert CaseStatus.DRAFT_READY == "draft_ready"

def test_channel_type_enum():
    """Test channel type enum values"""
    assert ChannelType.VOICE == "voice"
    assert ChannelType.SMS == "sms"
    assert ChannelType.EMAIL == "email"
    assert ChannelType.CHAT == "chat"
    assert ChannelType.WHATSAPP == "whatsapp"

def test_document_type_enum():
    """Test document type enum values"""
    assert DocumentType.EVIDENCE == "evidence"
    assert DocumentType.MEDICAL_RECORD == "medical_record"
    assert DocumentType.POLICE_REPORT == "police_report"
    assert DocumentType.INSURANCE_DOCUMENT == "insurance_document"
