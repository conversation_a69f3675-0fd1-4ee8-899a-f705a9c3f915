# Legal AI Platform Architecture

## System Overview

The Legal AI Platform is a comprehensive microservices-based system designed to automate legal intake, case management, and document generation. The system processes multi-channel client communications and uses AI to extract facts, generate legal documents, and manage approval workflows.

## Architecture Diagram

```
┌─────────────────────────────────────────────────────────────────┐
│                        Client Channels                          │
├─────────────────┬─────────────────┬─────────────────────────────┤
│   📞 Voice      │   💬 SMS/Chat   │      📧 Email               │
│   (<PERSON><PERSON><PERSON>)      │   (<PERSON><PERSON><PERSON>)      │   (SMTP/Webhooks)           │
└─────────────────┴─────────────────┴─────────────────────────────┘
         │                 │                        │
         ▼                 ▼                        ▼
┌─────────────────┬─────────────────┬─────────────────────────────┐
│  Voice Adapter  │  Chat Adapter   │    Email Adapter            │
│     :8004       │     :8005       │       :8006                 │
└─────────────────┴─────────────────┴─────────────────────────────┘
         │                 │                        │
         └─────────────────┼────────────────────────┘
                           ▼
                  ┌─────────────────┐
                  │  API Gateway    │
                  │     :8000       │
                  └─────────────────┘
                           │
                           ▼
                  ┌─────────────────┐
                  │  Case Router    │
                  │     :8002       │
                  └─────────────────┘
                           │
         ┌─────────────────┼─────────────────┐
         ▼                 ▼                 ▼
┌─────────────────┬─────────────────┬─────────────────┐
│ Intake Engine   │ File Ingestion  │ Reasoning Agent │
│     :8003       │     :8007       │     :8008       │
└─────────────────┴─────────────────┴─────────────────┘
         │                 │                 │
         └─────────────────┼─────────────────┘
                           ▼
                  ┌─────────────────┐
                  │  Session Hub    │
                  │     :8001       │
                  └─────────────────┘
                           │
         ┌─────────────────┼─────────────────┐
         ▼                 ▼                 ▼
┌─────────────────┬─────────────────┬─────────────────┐
│ Demand Generator│Complaint Generator│ Approval Service│
│     :8009       │     :8010       │     :8011       │
└─────────────────┴─────────────────┴─────────────────┘
                           │
                           ▼
                  ┌─────────────────┐
                  │Notification Svc │
                  │     :8012       │
                  └─────────────────┘
                           │
         ┌─────────────────┼─────────────────┐
         ▼                 ▼                 ▼
┌─────────────────┬─────────────────┬─────────────────┐
│   PostgreSQL    │     Redis       │       S3        │
│     :5432       │     :6379       │   (Interface)   │
└─────────────────┴─────────────────┴─────────────────┘
```

## Service Responsibilities

### Channel Adapters
- **Voice Adapter**: Handles Twilio voice webhooks, transcribes audio to text
- **Chat Adapter**: Processes SMS, WhatsApp, and web chat messages
- **Email Adapter**: Parses emails, extracts attachments, identifies case references

### Core Services
- **API Gateway**: Authentication, rate limiting, request routing, unified API
- **Session Hub**: Case session management, FSM state tracking, data persistence
- **Case Router**: Intelligent message routing based on content and case state

### AI Processing Services
- **Intake Engine**: LLM-powered fact extraction, follow-up question generation
- **File Ingestion**: OCR processing, document classification, evidence linking
- **Reasoning Agent**: Timeline construction, inconsistency detection, damage estimation

### Document Generation
- **Demand Generator**: Automated demand letter creation using templates
- **Complaint Generator**: Court-ready filing generation with legal formatting

### Workflow Services
- **Approval Service**: Multi-attorney approval workflows with auto-approval rules
- **Notification Service**: Multi-channel notifications (email, SMS, Slack)

## Data Flow

### 1. Message Ingestion
```
Client Message → Channel Adapter → Normalized Message → Case Router
```

### 2. Case Resolution
```
Case Router → Session Hub → Case Lookup/Creation → Session Data
```

### 3. Processing Pipeline
```
Normalized Message → Intake Engine → Fact Extraction → Session Hub
                  → File Ingestion → OCR + Classification → Document Storage
                  → Reasoning Agent → Analysis + Timeline → Case Updates
```

### 4. Document Generation
```
Case Data → Demand/Complaint Generator → Template Processing → Draft Document
```

### 5. Approval Workflow
```
Draft Document → Approval Service → Attorney Review → Notification Service
```

## State Management

### Case Status FSM
```
pending → in_progress → intake_complete → draft_ready → under_review → approved → filed → closed
```

### Session Data Structure
```json
{
  "case_id": "uuid",
  "current_step": "intake|processing|review",
  "pending_questions": ["question1", "question2"],
  "last_activity": "timestamp",
  "completeness_score": 0.85,
  "extracted_facts_count": 12
}
```

## Storage Architecture

### PostgreSQL Schema
- **cases**: Core case information and metadata
- **case_facts**: Extracted facts with confidence scores
- **timeline_events**: Chronological case events
- **documents**: File metadata and OCR results
- **messages**: Normalized message history
- **approval_requests**: Workflow approval tracking
- **notification_log**: Communication audit trail

### Redis Usage
- Session caching (TTL: 1 hour)
- Rate limiting counters
- Temporary processing state
- Recent activity cache

### S3 Storage (Interface)
- Document files (PDFs, images, audio)
- Generated legal documents
- Backup and archival

## Security Architecture

### Authentication & Authorization
- JWT-based authentication
- Role-based access control (attorney, admin, client)
- Service-to-service authentication

### Data Protection
- Encryption at rest (database)
- Encryption in transit (HTTPS/TLS)
- PII data handling compliance
- Audit logging

### Network Security
- Internal service communication
- API rate limiting
- Input validation and sanitization

## Scalability Considerations

### Horizontal Scaling
- Stateless service design
- Load balancer compatible
- Database connection pooling

### Performance Optimization
- Async/await throughout
- Database indexing strategy
- Redis caching layer
- File processing queues

### Monitoring & Observability
- Health check endpoints
- Structured logging
- Metrics collection points
- Error tracking

## Integration Points

### External Services
- **Twilio**: Voice and SMS webhooks
- **Email Providers**: SMTP/webhook integration
- **LLM Services**: Fact extraction and analysis
- **OCR Services**: Document text extraction
- **Slack**: Team notifications

### Webhook Endpoints
```
POST /webhooks/voice     - Twilio voice recordings
POST /webhooks/sms       - Twilio SMS messages
POST /webhooks/whatsapp  - WhatsApp messages
POST /webhooks/email     - Email ingestion
```

## Development Workflow

### Local Development
1. Start storage services: `docker-compose up postgres redis -d`
2. Run individual services: `uvicorn main:app --reload --port 8001`
3. Test with API client or curl commands

### Testing Strategy
- Unit tests for business logic
- Integration tests for service communication
- End-to-end tests for complete workflows
- Load testing for performance validation

### Deployment Pipeline
1. Code commit triggers CI/CD
2. Automated testing suite
3. Docker image building
4. Container orchestration deployment
5. Health check validation

## Configuration Management

### Environment Variables
- Database connections
- External service credentials
- Feature flags
- Performance tuning parameters

### Service Discovery
- Docker Compose networking
- Environment-based service URLs
- Health check based routing

## Error Handling & Resilience

### Retry Logic
- HTTP client retries with exponential backoff
- Circuit breaker pattern for external services
- Graceful degradation for non-critical features

### Fault Tolerance
- Service isolation
- Database transaction management
- Async processing with error queues
- Dead letter queues for failed messages

## Future Enhancements

### Planned Features
- Real-time case status dashboard
- Client portal for case updates
- Advanced AI reasoning capabilities
- Integration with court filing systems
- Mobile application support

### Scalability Improvements
- Kubernetes deployment
- Message queue system (RabbitMQ/Kafka)
- Microservice mesh (Istio)
- Advanced monitoring (Prometheus/Grafana)
